package log

const (
	LOG_DIR = "../../log/app.log"

	INFO     LogLevel = "INFO"     // info level log
	WARNING  LogLevel = "WARNING"  // warning level log
	ERROR    LogLevel = "ERROR"    // error level log
	REQUEST  LogLevel = "REQUEST"  // request level log
	RESPONSE LogLevel = "RESPONSE" // response level log
	GREEN    color    = "\033[32m"
	YELLOW   color    = "\033[33m"
	RED      color    = "\033[31m"
	CYAN     color    = "\033[36m"
	RESET    color    = "\033[0m"
)

var levelColor = map[LogLevel]logColor{
	INFO: {
		Start: GREEN,
		Reset: RESET,
	},
	WARNING: {
		Start: YELLOW,
		Reset: RESET,
	},
	ERROR: {
		Start: RED,
		Reset: RESET,
	},
	REQUEST: {
		Start: CYAN,
		Reset: RESET,
	},
	RESPONSE: {
		Start: CYAN,
		Reset: RESET,
	},
}
