package log

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"runtime"
	"time"
)

func setColor(level LogLevel) string {
	var (
		color    = levelColor[level]
		levelStr = fmt.Sprintf("[%v]", level)
	)

	return fmt.Sprint(color.Start, levelStr, color.Reset)
}

func logger(level LogLevel, message string, additionalInfo map[string]interface{}) {
	header := setColor(level)
	_, file, line, _ := runtime.Caller(2)
	funcName := fmt.Sprintf("%s:%d", file, line)

	currentTime := time.Now()
	newLog := logData{
		Level:          level,
		Timestamp:      currentTime,
		Caller:         funcName,
		Message:        message,
		AdditionalInfo: additionalInfo,
	}

	logByteInJson, err := json.MarshalIndent(newLog, "", "	")
	if err != nil {
		log.Println(header)
		fmt.Println(err.Error())
	}

	logByteInString, err := json.Marshal(newLog)
	if err != nil {
		log.Println(header)
		fmt.Println(err.Error())
	}

	logStringInJson := string(logByteInJson)
	logStringInString := string(logByteInString)
	logFile, err := os.OpenFile(LOG_DIR, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Println(header)
		fmt.Println(err.Error())
	}
	defer logFile.Close()

	log.SetOutput(logFile)

	fmt.Printf("%s\n%s", header, logStringInJson)
	log.SetFlags(0)
	log.Print(logStringInString)
}

// Generate error logger on terminal
func LogError(err error, additionalInfo map[string]interface{}) error {
	level := ERROR
	logger(level, err.Error(), additionalInfo)
	return err
}

// Generate warning logger on terminal
func LogWarning(message string, additionalInfo map[string]interface{}) {
	level := WARNING
	logger(level, message, additionalInfo)
}

// Generate info logger on terminal
func LogInfo(message string, additionalInfo map[string]interface{}) {
	level := INFO
	logger(level, message, additionalInfo)
}
