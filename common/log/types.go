package log

import "time"

// type for log severity level
type LogLevel string
type color string

type logData struct {
	Level          LogLevel    `json:"level,omitempty"`
	Timestamp      time.Time   `json:"timestamp,omitempty"`
	Caller         string      `json:"caller,omitempty"`
	Message        string      `json:"message,omitempty"`
	AdditionalInfo interface{} `json:"additionalInfo,omitempty"`
}

type logColor struct {
	Start color
	Reset color
}
