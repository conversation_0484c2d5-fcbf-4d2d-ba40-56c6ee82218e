package constanta

const (
	Success      = "Success"
	Unauthorized = "無許可"

	EmptyAuth         = "認証が空"
	InvalidAuth       = "認証が無効"
	UnexpectedSigning = "不適切な署名方法: %v"
	UserInexist       = "ユーザーが存在しません"

	ServiceRunning = "service is running"
	ServiceBroken  = "service broken"

	HeaderKeyContentType = "Content-Type"

	EnvDevelopment = "development"
)

const (
	RoleWorker     = "worker"
	RoleSupervisor = "supervisor"
	RoleSubAdmin   = "sub admin"
	RoleAdmin      = "admin"
	RoleSuperAdmin = "super admin"
)
