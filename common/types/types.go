package types

type BasicResp struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

type BasicRespWithPagination struct {
	Message string         `json:"message"`
	Data    interface{}    `json:"data,omitempty"`
	Meta    MetaPagination `json:"meta,omitempty"`
}

type MetaPagination struct {
	PageNumber   int64 `json:"page_number"`
	PageSize     int64 `json:"page_size"`
	TotalPages   int64 `json:"total_pages"`
	TotalRecords int64 `json:"total_records"`
}

type BasicGetParam struct {
	SortBy     string `query:"sort_by"`
	OrderBy    string `query:"order_by"`
	Search     string `query:"search"`
	PageNumber int64  `query:"page_number"`
	PageSize   int64  `query:"page_size"`
}
