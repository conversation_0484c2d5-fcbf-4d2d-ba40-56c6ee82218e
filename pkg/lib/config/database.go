package config

import (
	"fmt"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

func InitDatabase() {
	config := GetConfig()
	dsn := fmt.Sprintf("postgres://%s:%s@%s:%s/%s", config.DBUser, config.DBPassword, config.DBHost, config.DBPort, config.DBName)
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
	})
	if err != nil {
		panic(fmt.Sprintf("DB Connection Error: %s", err.Error()))
	}

	if config.Env == constanta.EnvDevelopment {
		db.Logger = logger.Default
	}
	// db.AutoMigrate(&model.Example{}) // for migration

	// Register global DB instance for other packages without causing import cycles
	dbmanager.Set(db)

	fmt.Println("Connected to Database")
}
