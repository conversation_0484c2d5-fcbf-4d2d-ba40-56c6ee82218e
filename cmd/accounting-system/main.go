package main

import (
	"log"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/config"
	"github.com/Sera-Global/be-nbs-accounting-system/server"
	"github.com/Sera-Global/be-nbs-accounting-system/server/http"
)

func main() {
	err := server.Init("http")
	if err != nil {
		log.Fatal(err)
	}

	e := http.InitHttp()

	// start go admin
	err = config.GoAdminManager().Use(e)
	if err != nil {
		log.Fatal(err)
	}

	e.Logger.Fatal(e.Start(":" + config.GetConfig().Port))
}
