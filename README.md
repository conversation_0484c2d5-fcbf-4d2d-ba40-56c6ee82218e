# NBS Accounting System

This is a Go-based backend service for the NBS Accounting System. It provides RESTful APIs, admin management via GoAdmin, and connects to PostgreSQL.

## Features
- RESTful API built with [Echo](https://echo.labstack.com/)
- Admin dashboard via [GoAdmin](https://github.com/GoAdminGroup/go-admin)
- PostgreSQL database integration
- JWT-based authentication
- Modular domain-driven structure

## Project Structure
```
be-nbs-accounting-system/
├── cmd/accounting-system/       # Main entrypoint
├── common/                      # Common utilities (logging, constants)
├── domain/                      # Domain logic
├── files/                       # Configs, migrations
├── pkg/                         # Libraries and shared packages
├── server/                      # Server initialization and HTTP setup
├── usecase/                     # Business logic
```

## Prerequisites
- Go 1.18+
- PostgreSQL database

## Setup
1. **Clone the repository:**
   ```bash
   git clone <repo-url>
   cd be-nbs-accounting-system
   ```

2. **Install Go dependencies:**
   ```bash
   go mod tidy
   ```

3. **Configure environment variables:**
   - Copy the example env file:
     ```bash
     cp files/conf/env.example files/conf/.env
     ```
   - Edit `files/conf/.env` to set your DB and JWT credentials.

4. **Setup the database:**
   - Create a PostgreSQL database.
   - Run the migration script:
     ```bash
     psql -U <dbuser> -d <dbname> -f files/database/migration_postgres.sql
     ```

## Running the App

### Development
```bash
cd cmd/accounting-system
GO_ENV=development go run main.go
```

### Production
Build the binary:
```bash
go build -o accounting-system ./cmd/accounting-system
```
Run it:
```bash
./accounting-system
```

## API Endpoints
- `GET /v1/health` — Health check endpoint

## Admin Panel
Accessible at `/admin` after running the server. Use credentials seeded in the migration or created via GoAdmin.

## Logging
Logs are written to `log/app.log`.
