package distantfee

import (
	"context"
	"errors"
	"testing"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	distantFeeDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/distant_fee"
)

// Mock implementation of DistantFeeDomainItf for testing
type mockDistantFeeDomain struct {
	distantFees []distantFeeDmn.DistantFee
	err         error
}

func (m *mockDistantFeeDomain) GetList(ctx context.Context, param distantFeeDmn.GetListParam) ([]distantFeeDmn.DistantFee, error) {
	if m.err != nil {
		return nil, m.err
	}
	return m.distantFees, nil
}

func (m *mockDistantFeeDomain) GetByID(ctx context.Context, param distantFeeDmn.GetByIDParam) (distantFeeDmn.DistantFee, error) {
	if m.err != nil {
		return distantFeeDmn.DistantFee{}, m.err
	}
	for _, fee := range m.distantFees {
		if fee.ID == param.ID {
			return fee, nil
		}
	}
	return distantFeeDmn.DistantFee{}, nil
}

func TestDistantFeeUseCase_GetList_Success(t *testing.T) {
	// Test data
	mockData := []distantFeeDmn.DistantFee{
		{
			ID:    1,
			Title: "Distant Fee 1",
		},
		{
			ID:    2,
			Title: "Distant Fee 2",
		},
		{
			ID:    3,
			Title: "Distant Fee 3",
		},
	}

	mockDomain := &mockDistantFeeDomain{
		distantFees: mockData,
		err:         nil,
	}

	uc := &DistantFeeUseCase{
		distantFee: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 3 {
		t.Errorf("Expected 3 results, got %d", len(result))
	}

	// Check first item
	if result[0].ID != 1 {
		t.Errorf("Expected ID 1, got %d", result[0].ID)
	}

	if result[0].Name != "Distant Fee 1" {
		t.Errorf("Expected Name 'Distant Fee 1', got %s", result[0].Name)
	}

	// Check second item
	if result[1].ID != 2 {
		t.Errorf("Expected ID 2, got %d", result[1].ID)
	}

	if result[1].Name != "Distant Fee 2" {
		t.Errorf("Expected Name 'Distant Fee 2', got %s", result[1].Name)
	}

	// Check third item
	if result[2].ID != 3 {
		t.Errorf("Expected ID 3, got %d", result[2].ID)
	}

	if result[2].Name != "Distant Fee 3" {
		t.Errorf("Expected Name 'Distant Fee 3', got %s", result[2].Name)
	}
}

func TestDistantFeeUseCase_GetList_WithSearch_Success(t *testing.T) {
	// Test data - mock returns filtered results (simulating domain layer filtering)
	mockData := []distantFeeDmn.DistantFee{
		{
			ID:    1,
			Title: "Distant Fee AA",
		},
		{
			ID:    2,
			Title: "Distant Fee AB",
		},
	}

	mockDomain := &mockDistantFeeDomain{
		distantFees: mockData,
		err:         nil,
	}

	uc := &DistantFeeUseCase{
		distantFee: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "Distant Fee A",
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}

	// Check that results contain the filtered items
	expectedTitles := []string{"Distant Fee AA", "Distant Fee AB"}
	for i, res := range result {
		if res.Name != expectedTitles[i] {
			t.Errorf("Expected Name '%s', got %s", expectedTitles[i], res.Name)
		}
	}
}

func TestDistantFeeUseCase_GetList_EmptyResult(t *testing.T) {
	mockDomain := &mockDistantFeeDomain{
		distantFees: []distantFeeDmn.DistantFee{},
		err:         nil,
	}

	uc := &DistantFeeUseCase{
		distantFee: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results, got %d", len(result))
	}
}

func TestDistantFeeUseCase_GetList_DomainError(t *testing.T) {
	mockDomain := &mockDistantFeeDomain{
		distantFees: nil,
		err:         errors.New("database connection failed"),
	}

	uc := &DistantFeeUseCase{
		distantFee: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err == nil {
		t.Error("Expected error, got nil")
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results, got %d", len(result))
	}

	if err.Error() != "database connection failed" {
		t.Errorf("Expected error message 'database connection failed', got %s", err.Error())
	}
}

func TestDistantFeeUseCase_GetList_ResponseMapping(t *testing.T) {
	// Test that Title field is correctly mapped to Name in response
	mockData := []distantFeeDmn.DistantFee{
		{
			ID:               1,
			Code:             "DIST001",
			Title:            "Distant Fee Pattern 1",
			Explanation:      "Test explanation",
			Unit:             2.5,
			AddAmountPerHour: 1500.75,
		},
	}

	mockDomain := &mockDistantFeeDomain{
		distantFees: mockData,
		err:         nil,
	}

	uc := &DistantFeeUseCase{
		distantFee: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 1 {
		t.Errorf("Expected 1 result, got %d", len(result))
	}

	// Check that Title is mapped to Name
	if result[0].Name != "Distant Fee Pattern 1" {
		t.Errorf("Expected Name 'Distant Fee Pattern 1', got %s", result[0].Name)
	}

	// Check that ID is correctly mapped
	if result[0].ID != 1 {
		t.Errorf("Expected ID 1, got %d", result[0].ID)
	}
}

func TestDistantFeeUseCase_GetList_WithSearchEmptyString(t *testing.T) {
	// Test with empty search string - should return all results
	mockData := []distantFeeDmn.DistantFee{
		{
			ID:    1,
			Title: "Distant Fee AA",
		},
		{
			ID:    2,
			Title: "Distant Fee BB",
		},
	}

	mockDomain := &mockDistantFeeDomain{
		distantFees: mockData,
		err:         nil,
	}

	uc := &DistantFeeUseCase{
		distantFee: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "", // Empty search
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}
}

func TestDistantFeeUseCase_GetList_WithSearchCaseInsensitive(t *testing.T) {
	// Test case-insensitive search
	mockData := []distantFeeDmn.DistantFee{
		{
			ID:    1,
			Title: "Distant Fee Premium",
		},
		{
			ID:    2,
			Title: "Basic Distant Fee",
		},
	}

	mockDomain := &mockDistantFeeDomain{
		distantFees: mockData,
		err:         nil,
	}

	uc := &DistantFeeUseCase{
		distantFee: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "distant fee", // lowercase search
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}
}

func TestDistantFeeUseCase_GetList_NilDistantFees(t *testing.T) {
	// Test when domain returns nil slice
	mockDomain := &mockDistantFeeDomain{
		distantFees: nil,
		err:         nil,
	}

	uc := &DistantFeeUseCase{
		distantFee: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if result == nil {
		t.Error("Expected non-nil result slice")
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results, got %d", len(result))
	}
}

func TestDistantFeeUseCase_GetList_SingleResult(t *testing.T) {
	// Test with single result
	mockData := []distantFeeDmn.DistantFee{
		{
			ID:    42,
			Title: "Single Distant Fee",
		},
	}

	mockDomain := &mockDistantFeeDomain{
		distantFees: mockData,
		err:         nil,
	}

	uc := &DistantFeeUseCase{
		distantFee: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 1 {
		t.Errorf("Expected 1 result, got %d", len(result))
	}

	if result[0].ID != 42 {
		t.Errorf("Expected ID 42, got %d", result[0].ID)
	}

	if result[0].Name != "Single Distant Fee" {
		t.Errorf("Expected Name 'Single Distant Fee', got %s", result[0].Name)
	}
}

func TestDistantFeeUseCase_GetList_LargeDataset(t *testing.T) {
	// Test with larger dataset to ensure performance
	mockData := make([]distantFeeDmn.DistantFee, 100)
	for i := 0; i < 100; i++ {
		mockData[i] = distantFeeDmn.DistantFee{
			ID:    int64(i + 1),
			Title: "Distant Fee " + string(rune(i+65)), // A, B, C, etc.
		}
	}

	mockDomain := &mockDistantFeeDomain{
		distantFees: mockData,
		err:         nil,
	}

	uc := &DistantFeeUseCase{
		distantFee: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 100 {
		t.Errorf("Expected 100 results, got %d", len(result))
	}

	// Check first and last items
	if result[0].ID != 1 {
		t.Errorf("Expected first ID 1, got %d", result[0].ID)
	}

	if result[99].ID != 100 {
		t.Errorf("Expected last ID 100, got %d", result[99].ID)
	}
}
