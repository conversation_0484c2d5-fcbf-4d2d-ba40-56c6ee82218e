package distantfee

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	distantfee "github.com/Sera-Global/be-nbs-accounting-system/domain/distant_fee"
)

func (uc *DistantFeeUseCase) GetList(ctx context.Context, req GetListReq) ([]GetListResp, error) {
	// Get distant fees from domain
	distantFees, err := uc.distantFee.GetList(ctx, distantfee.GetListParam{
		BasicGetParam: req.BasicGetParam,
	})
	if err != nil {
		return []GetListResp{}, log.LogError(err, nil)
	}
	if len(distantFees) == 0 {
		return []GetListResp{}, nil
	}

	// Convert to response format
	var resp []GetListResp
	for _, fee := range distantFees {
		resp = append(resp, GetListResp{
			ID:   fee.ID,
			Name: fee.Title,
		})
	}

	return resp, nil
}
