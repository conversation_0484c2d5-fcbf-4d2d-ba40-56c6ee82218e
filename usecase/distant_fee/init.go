package distantfee

import (
	distantFeeDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/distant_fee"
)

type DistantFeeUseCase struct {
	distantFee distantFeeDmn.DistantFeeDomainItf
}

type Domains struct {
	DistantFeeDomain distantFeeDmn.DistantFeeDomainItf
}

func InitDistantFeeUseCase(d Domains) *DistantFeeUseCase {
	uc := &DistantFeeUseCase{
		distantFee: d.DistantFeeDomain,
	}
	return uc
}
