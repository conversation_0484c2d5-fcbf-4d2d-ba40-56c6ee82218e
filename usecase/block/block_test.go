package block

import (
	"context"
	"errors"
	"testing"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	blockDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/block"
)

// Mock implementation of BlockDomainItf for testing
type mockBlockDomain struct {
	blocks []blockDmn.BlockWithDistricts
	err    error
}

func (m *mockBlockDomain) GetListWithDistricts(ctx context.Context, param blockDmn.GetListReq) ([]blockDmn.BlockWithDistricts, error) {
	if m.err != nil {
		return nil, m.err
	}

	// Filter by search if provided
	if param.Search != "" {
		var filtered []blockDmn.BlockWithDistricts
		for _, block := range m.blocks {
			// Simple case-insensitive search simulation
			if containsIgnoreCase(block.Name, param.Search) {
				filtered = append(filtered, block)
			}
		}
		return filtered, nil
	}

	return m.blocks, nil
}

// Helper function to simulate case-insensitive search
func containsIgnoreCase(str, substr string) bool {
	// Simple implementation for testing
	return len(substr) <= len(str) && str[:len(substr)] == substr
}

func TestBlockUseCase_GetList_Success(t *testing.T) {
	// Test data
	mockData := []blockDmn.BlockWithDistricts{
		{
			ID:        1,
			Name:      "Bantul",
			Districts: []string{"Banguntapan", "Jetis"},
		},
		{
			ID:        2,
			Name:      "Sleman",
			Districts: []string{"Maguwoharjo", "Minomartani", "Ngaglik"},
		},
		{
			ID:        3,
			Name:      "Yogyakarta",
			Districts: []string{"Gondokusuman", "Jetis"},
		},
	}

	mockDomain := &mockBlockDomain{
		blocks: mockData,
		err:    nil,
	}

	uc := &BlockUseCase{
		block: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 3 {
		t.Errorf("Expected 3 results, got %d", len(result))
	}

	// Check first item
	if result[0].ID != 1 {
		t.Errorf("Expected ID 1, got %d", result[0].ID)
	}

	if result[0].Name != "Bantul" {
		t.Errorf("Expected name 'Bantul', got %s", result[0].Name)
	}

	if len(result[0].District) != 2 {
		t.Errorf("Expected 2 districts, got %d", len(result[0].District))
	}

	if result[0].District[0] != "Banguntapan" {
		t.Errorf("Expected first district 'Banguntapan', got %s", result[0].District[0])
	}
}

func TestBlockUseCase_GetList_WithSearch_Success(t *testing.T) {
	// Test data
	mockData := []blockDmn.BlockWithDistricts{
		{
			ID:        1,
			Name:      "Bantul",
			Districts: []string{"Banguntapan", "Jetis"},
		},
		{
			ID:        2,
			Name:      "Sleman",
			Districts: []string{"Maguwoharjo", "Minomartani", "Ngaglik"},
		},
		{
			ID:        3,
			Name:      "Yogyakarta",
			Districts: []string{"Gondokusuman", "Jetis"},
		},
	}

	mockDomain := &mockBlockDomain{
		blocks: mockData,
		err:    nil,
	}

	uc := &BlockUseCase{
		block: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "Ban",
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 1 {
		t.Errorf("Expected 1 result, got %d", len(result))
	}

	if result[0].Name != "Bantul" {
		t.Errorf("Expected name 'Bantul', got %s", result[0].Name)
	}
}

func TestBlockUseCase_GetList_WithSearch_NoResults(t *testing.T) {
	// Test data
	mockData := []blockDmn.BlockWithDistricts{
		{
			ID:        1,
			Name:      "Bantul",
			Districts: []string{"Banguntapan", "Jetis"},
		},
		{
			ID:        2,
			Name:      "Sleman",
			Districts: []string{"Maguwoharjo", "Minomartani", "Ngaglik"},
		},
	}

	mockDomain := &mockBlockDomain{
		blocks: mockData,
		err:    nil,
	}

	uc := &BlockUseCase{
		block: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "NonExistent",
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results, got %d", len(result))
	}
}

func TestBlockUseCase_GetList_EmptyData(t *testing.T) {
	mockDomain := &mockBlockDomain{
		blocks: []blockDmn.BlockWithDistricts{},
		err:    nil,
	}

	uc := &BlockUseCase{
		block: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results, got %d", len(result))
	}
}

func TestBlockUseCase_GetList_DomainError(t *testing.T) {
	expectedError := errors.New("database connection failed")

	mockDomain := &mockBlockDomain{
		blocks: nil,
		err:    expectedError,
	}

	uc := &BlockUseCase{
		block: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err == nil {
		t.Error("Expected error, got nil")
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results on error, got %d", len(result))
	}
}

func TestBlockUseCase_GetList_ResponseMapping(t *testing.T) {
	// Test that fields are correctly mapped in response
	mockData := []blockDmn.BlockWithDistricts{
		{
			ID:        1,
			Name:      "Test Block",
			Districts: []string{"District A", "District B", "District C"},
		},
	}

	mockDomain := &mockBlockDomain{
		blocks: mockData,
		err:    nil,
	}

	uc := &BlockUseCase{
		block: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 1 {
		t.Errorf("Expected 1 result, got %d", len(result))
	}

	// Check field mapping
	if result[0].ID != 1 {
		t.Errorf("Expected ID 1, got %d", result[0].ID)
	}

	if result[0].Name != "Test Block" {
		t.Errorf("Expected name 'Test Block', got %s", result[0].Name)
	}

	if len(result[0].District) != 3 {
		t.Errorf("Expected 3 districts, got %d", len(result[0].District))
	}

	expectedDistricts := []string{"District A", "District B", "District C"}
	for i, district := range result[0].District {
		if district != expectedDistricts[i] {
			t.Errorf("Expected district %s, got %s", expectedDistricts[i], district)
		}
	}
}

func TestBlockUseCase_GetList_WithSearchEmptyString(t *testing.T) {
	// Test with empty search string - should return all results
	mockData := []blockDmn.BlockWithDistricts{
		{
			ID:        1,
			Name:      "Bantul",
			Districts: []string{"Banguntapan"},
		},
		{
			ID:        2,
			Name:      "Sleman",
			Districts: []string{"Maguwoharjo"},
		},
	}

	mockDomain := &mockBlockDomain{
		blocks: mockData,
		err:    nil,
	}

	uc := &BlockUseCase{
		block: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "", // Empty search
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}
}

func TestBlockUseCase_GetList_BlockWithNoDistricts(t *testing.T) {
	// Test block with empty districts array
	mockData := []blockDmn.BlockWithDistricts{
		{
			ID:        1,
			Name:      "Empty Block",
			Districts: []string{},
		},
	}

	mockDomain := &mockBlockDomain{
		blocks: mockData,
		err:    nil,
	}

	uc := &BlockUseCase{
		block: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 1 {
		t.Errorf("Expected 1 result, got %d", len(result))
	}

	if len(result[0].District) != 0 {
		t.Errorf("Expected 0 districts, got %d", len(result[0].District))
	}
}

func TestBlockUseCase_GetList_MultipleBlocksWithSearch(t *testing.T) {
	// Test multiple blocks matching search criteria
	mockData := []blockDmn.BlockWithDistricts{
		{
			ID:        1,
			Name:      "Bantul",
			Districts: []string{"Banguntapan", "Jetis"},
		},
		{
			ID:        2,
			Name:      "Banjar",
			Districts: []string{"Banjar Tengah"},
		},
		{
			ID:        3,
			Name:      "Sleman",
			Districts: []string{"Maguwoharjo"},
		},
	}

	mockDomain := &mockBlockDomain{
		blocks: mockData,
		err:    nil,
	}

	uc := &BlockUseCase{
		block: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "Ban",
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}

	// Check that both Bantul and Banjar are returned
	names := make(map[string]bool)
	for _, block := range result {
		names[block.Name] = true
	}

	if !names["Bantul"] {
		t.Error("Expected Bantul in results")
	}

	if !names["Banjar"] {
		t.Error("Expected Banjar in results")
	}
}
