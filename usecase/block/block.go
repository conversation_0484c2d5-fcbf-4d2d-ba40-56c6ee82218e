package block

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	blockDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/block"
)

func (uc *BlockUseCase) GetList(ctx context.Context, req GetListReq) ([]GetListResp, error) {
	// Get blocks with districts from domain
	blocks, err := uc.block.GetListWithDistricts(ctx, blockDmn.GetListReq{
		BasicGetParam: req.BasicGetParam,
	})
	if err != nil {
		return []GetListResp{}, log.LogError(err, nil)
	}
	if len(blocks) == 0 {
		return []GetListResp{}, nil
	}

	// Convert to response format
	var resp []GetListResp
	for _, block := range blocks {
		resp = append(resp, GetListResp{
			ID:       block.ID,
			Name:     block.Name,
			District: block.Districts,
		})
	}

	return resp, nil
}
