package qualification

import (
	"context"
	"errors"
	"strings"
	"testing"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	qualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/qualification"
)

// Mock domain for testing
type mockQualificationDomain struct {
	qualifications []qualificationDmn.Qualification
	err            error
}

func (m *mockQualificationDomain) GetList(ctx context.Context, param qualificationDmn.GetListReq) ([]qualificationDmn.Qualification, error) {
	if m.err != nil {
		return []qualificationDmn.Qualification{}, m.err
	}

	// Filter by search if provided
	if param.Search != "" {
		var filtered []qualificationDmn.Qualification
		for _, qual := range m.qualifications {
			// Simple case-insensitive contains check for testing
			if contains(qual.Title, param.Search) {
				filtered = append(filtered, qual)
			}
		}
		return filtered, nil
	}

	return m.qualifications, nil
}

func (m *mockQualificationDomain) GetByIDs(ctx context.Context, param qualificationDmn.GetByIDsParam) ([]qualificationDmn.Qualification, error) {
	if m.err != nil {
		return []qualificationDmn.Qualification{}, m.err
	}

	var filtered []qualificationDmn.Qualification
	for _, qual := range m.qualifications {
		for _, id := range param.IDs {
			if qual.ID == id {
				filtered = append(filtered, qual)
			}
		}
	}
	return filtered, nil
}

// Helper function for case-insensitive contains check
func contains(str, substr string) bool {
	// Convert both strings to lowercase for case-insensitive comparison
	strLower := strings.ToLower(str)
	substrLower := strings.ToLower(substr)

	// Simple implementation for testing
	for i := 0; i <= len(strLower)-len(substrLower); i++ {
		if strLower[i:i+len(substrLower)] == substrLower {
			return true
		}
	}
	return false
}

func TestQualificationUseCase_GetList_Success(t *testing.T) {
	// Test data
	mockData := []qualificationDmn.Qualification{
		{
			ID:    1,
			Title: "Qualification 1",
		},
		{
			ID:    2,
			Title: "Qualification 2",
		},
		{
			ID:    3,
			Title: "Qualification 3",
		},
	}

	mockDomain := &mockQualificationDomain{
		qualifications: mockData,
		err:            nil,
	}

	uc := &QualificationUseCase{
		qualification: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 3 {
		t.Errorf("Expected 3 results, got %d", len(result))
	}

	// Check first item
	if result[0].ID != 1 {
		t.Errorf("Expected ID 1, got %d", result[0].ID)
	}

	if result[0].Name != "Qualification 1" {
		t.Errorf("Expected Name 'Qualification 1', got %s", result[0].Name)
	}

	// Check second item
	if result[1].ID != 2 {
		t.Errorf("Expected ID 2, got %d", result[1].ID)
	}

	if result[1].Name != "Qualification 2" {
		t.Errorf("Expected Name 'Qualification 2', got %s", result[1].Name)
	}

	// Check third item
	if result[2].ID != 3 {
		t.Errorf("Expected ID 3, got %d", result[2].ID)
	}

	if result[2].Name != "Qualification 3" {
		t.Errorf("Expected Name 'Qualification 3', got %s", result[2].Name)
	}
}

func TestQualificationUseCase_GetList_WithSearch_Success(t *testing.T) {
	// Test data
	mockData := []qualificationDmn.Qualification{
		{
			ID:    1,
			Title: "Qualification AA",
		},
		{
			ID:    2,
			Title: "Qualification AB",
		},
		{
			ID:    3,
			Title: "Qualification BB",
		},
	}

	mockDomain := &mockQualificationDomain{
		qualifications: mockData,
		err:            nil,
	}

	uc := &QualificationUseCase{
		qualification: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "Qualification A",
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}

	// Check that results contain the filtered items
	expectedTitles := []string{"Qualification AA", "Qualification AB"}
	for i, res := range result {
		if res.Name != expectedTitles[i] {
			t.Errorf("Expected Name '%s', got %s", expectedTitles[i], res.Name)
		}
	}
}

func TestQualificationUseCase_GetList_EmptyResult(t *testing.T) {
	mockDomain := &mockQualificationDomain{
		qualifications: []qualificationDmn.Qualification{},
		err:            nil,
	}

	uc := &QualificationUseCase{
		qualification: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results, got %d", len(result))
	}
}

func TestQualificationUseCase_GetList_DomainError(t *testing.T) {
	mockDomain := &mockQualificationDomain{
		qualifications: nil,
		err:            errors.New("database connection failed"),
	}

	uc := &QualificationUseCase{
		qualification: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err == nil {
		t.Error("Expected error, got nil")
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results, got %d", len(result))
	}

	if err.Error() != "database connection failed" {
		t.Errorf("Expected error message 'database connection failed', got %s", err.Error())
	}
}

func TestQualificationUseCase_GetList_ResponseMapping(t *testing.T) {
	// Test that Title field is correctly mapped to Name in response
	mockData := []qualificationDmn.Qualification{
		{
			ID:          1,
			Code:        "QUAL001",
			Title:       "Qualification Pattern 1",
			Explanation: "Test explanation",
			AddClaim:    7500.50,
			PaidAmount:  6000.25,
		},
	}

	mockDomain := &mockQualificationDomain{
		qualifications: mockData,
		err:            nil,
	}

	uc := &QualificationUseCase{
		qualification: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 1 {
		t.Errorf("Expected 1 result, got %d", len(result))
	}

	// Check that Title is mapped to Name
	if result[0].Name != "Qualification Pattern 1" {
		t.Errorf("Expected Name 'Qualification Pattern 1', got %s", result[0].Name)
	}

	// Check that ID is correctly mapped
	if result[0].ID != 1 {
		t.Errorf("Expected ID 1, got %d", result[0].ID)
	}
}

func TestQualificationUseCase_GetList_WithSearchEmptyString(t *testing.T) {
	// Test with empty search string - should return all results
	mockData := []qualificationDmn.Qualification{
		{
			ID:    1,
			Title: "Qualification AA",
		},
		{
			ID:    2,
			Title: "Qualification BB",
		},
	}

	mockDomain := &mockQualificationDomain{
		qualifications: mockData,
		err:            nil,
	}

	uc := &QualificationUseCase{
		qualification: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "", // Empty search
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}
}

func TestQualificationUseCase_GetList_WithSearchCaseInsensitive(t *testing.T) {
	// Test case-insensitive search
	mockData := []qualificationDmn.Qualification{
		{
			ID:    1,
			Title: "Qualification Premium",
		},
		{
			ID:    2,
			Title: "Basic Qualification",
		},
	}

	mockDomain := &mockQualificationDomain{
		qualifications: mockData,
		err:            nil,
	}

	uc := &QualificationUseCase{
		qualification: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "qualification", // lowercase search
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}
}
