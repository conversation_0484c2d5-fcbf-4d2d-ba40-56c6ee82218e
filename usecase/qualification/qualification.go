package qualification

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	qualification "github.com/Sera-Global/be-nbs-accounting-system/domain/qualification"
)

func (uc *QualificationUseCase) GetList(ctx context.Context, req GetListReq) ([]GetListResp, error) {
	// Get qualifications from domain
	qualifications, err := uc.qualification.GetList(ctx, qualification.GetListReq{
		BasicGetParam: req.BasicGetParam,
	})
	if err != nil {
		return []GetListResp{}, log.LogError(err, nil)
	}
	if len(qualifications) == 0 {
		return []GetListResp{}, nil
	}

	// Convert to response format
	var resp []GetListResp
	for _, qual := range qualifications {
		resp = append(resp, GetListResp{
			ID:   qual.ID,
			Name: qual.Title,
		})
	}

	return resp, nil
}
