package qualification

import (
	qualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/qualification"
)

type QualificationUseCase struct {
	qualification qualificationDmn.QualificationDomainItf
}

type Domains struct {
	QualificationDomain qualificationDmn.QualificationDomainItf
}

func InitQualificationUseCase(d Domains) *QualificationUseCase {
	uc := &QualificationUseCase{
		qualification: d.QualificationDomain,
	}
	return uc
}
