package health

import (
	"context"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	ers "github.com/Sera-Global/be-nbs-accounting-system/common/errors"
	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

func (uc *HealthUseCase) Health(ctx context.Context) (HealthCheck, error) {
	health, err := uc.health.GetHealth(ctx)
	if err != nil {
		return HealthCheck{}, log.LogError(err, nil)
	}

	if !health.IsHealth {
		err = ers.ErrServiceBroken
		return HealthCheck{}, log.LogError(err, nil)
	}

	resp := HealthCheck{
		Message: constanta.ServiceRunning,
	}

	return resp, nil
}
