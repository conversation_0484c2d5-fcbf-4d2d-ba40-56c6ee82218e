package option

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	option "github.com/Sera-Global/be-nbs-accounting-system/domain/option"
)

func (uc *OptionUseCase) GetList(ctx context.Context, req GetListReq) ([]GetListResp, error) {
	// Get options from domain
	options, err := uc.option.GetList(ctx, option.GetListReq{
		BasicGetParam: req.BasicGetParam,
	})
	if err != nil {
		return []GetListResp{}, log.LogError(err, nil)
	}
	if len(options) == 0 {
		return []GetListResp{}, nil
	}

	// Convert to response format
	var resp []GetListResp
	for _, opt := range options {
		resp = append(resp, GetListResp{
			ID:       opt.ID,
			Name:     opt.Title,
			AddClaim: opt.AddClaim,
		})
	}

	return resp, nil
}
