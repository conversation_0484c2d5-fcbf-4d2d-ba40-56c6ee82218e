package basicprice

import (
	basicPriceDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
)

type BasicPriceUseCase struct {
	basicPrice basicPriceDmn.BasicPriceDomainItf
}

type Domains struct {
	BasicPriceDomain basicPriceDmn.BasicPriceDomainItf
}

func InitBasicPriceUseCase(d Domains) *BasicPriceUseCase {
	uc := &BasicPriceUseCase{
		basicPrice: d.BasicPriceDomain,
	}
	return uc
}
