package basicprice

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	basicprice "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
)

func (uc *BasicPriceUseCase) GetList(ctx context.Context, req GetListReq) ([]GetListResp, error) {
	// Get basic prices from domain
	basicPrices, err := uc.basicPrice.GetList(ctx, basicprice.GetListReq{
		BasicGetParam: req.BasicGetParam,
	})
	if err != nil {
		return []GetListResp{}, log.LogError(err, nil)
	}
	if len(basicPrices) == 0 {
		return []GetListResp{}, nil
	}

	// Convert to response format
	var resp []GetListResp
	for _, basicPrice := range basicPrices {
		resp = append(resp, GetListResp{
			ID:   basicPrice.ID,
			Name: basicPrice.Title,
		})
	}

	return resp, nil
}
