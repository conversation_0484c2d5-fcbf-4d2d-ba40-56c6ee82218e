package basicprice

import (
	"context"
	"errors"
	"testing"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	basicpriceDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
)

// Mock domain for testing
type mockBasicPriceDomain struct {
	basicPrices []basicpriceDmn.BasicPrice
	basicPrice  basicpriceDmn.BasicPrice
	err         error
}

func (m *mockBasicPriceDomain) GetList(ctx context.Context, param basicpriceDmn.GetListReq) ([]basicpriceDmn.BasicPrice, error) {
	if m.err != nil {
		return []basicpriceDmn.BasicPrice{}, m.err
	}

	// Filter by search if provided
	if param.Search != "" {
		var filtered []basicpriceDmn.BasicPrice
		for _, bp := range m.basicPrices {
			// Simple case-insensitive contains check for testing
			if contains(bp.Title, param.Search) {
				filtered = append(filtered, bp)
			}
		}
		return filtered, nil
	}

	return m.basicPrices, nil
}

func (m *mockBasicPriceDomain) GetByID(ctx context.Context, param basicpriceDmn.GetByIDParam) (basicpriceDmn.BasicPrice, error) {
	if m.err != nil {
		return basicpriceDmn.BasicPrice{}, m.err
	}
	return m.basicPrice, nil
}

// Helper function for case-insensitive contains check
func contains(s, substr string) bool {
	// Simple implementation for testing
	return len(s) >= len(substr) &&
		(s == substr ||
			(len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					findInString(s, substr))))
}

func findInString(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func TestBasicPriceUseCase_GetList_Success(t *testing.T) {
	// Test data
	mockData := []basicpriceDmn.BasicPrice{
		{
			ID:    1,
			Title: "Price AA",
		},
		{
			ID:    2,
			Title: "Price AB",
		},
		{
			ID:    3,
			Title: "Price BB",
		},
	}

	mockDomain := &mockBasicPriceDomain{
		basicPrices: mockData,
		err:         nil,
	}

	uc := &BasicPriceUseCase{
		basicPrice: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 3 {
		t.Errorf("Expected 3 results, got %d", len(result))
	}

	// Check first item
	if result[0].ID != 1 {
		t.Errorf("Expected ID 1, got %d", result[0].ID)
	}

	if result[0].Name != "Price AA" {
		t.Errorf("Expected name 'Price AA', got '%s'", result[0].Name)
	}

	// Check second item
	if result[1].ID != 2 {
		t.Errorf("Expected ID 2, got %d", result[1].ID)
	}

	if result[1].Name != "Price AB" {
		t.Errorf("Expected name 'Price AB', got '%s'", result[1].Name)
	}
}

func TestBasicPriceUseCase_GetList_WithSearch_Success(t *testing.T) {
	// Test data
	mockData := []basicpriceDmn.BasicPrice{
		{
			ID:    1,
			Title: "Price AA",
		},
		{
			ID:    2,
			Title: "Price AB",
		},
		{
			ID:    3,
			Title: "Price BB",
		},
	}

	mockDomain := &mockBasicPriceDomain{
		basicPrices: mockData,
		err:         nil,
	}

	uc := &BasicPriceUseCase{
		basicPrice: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "Price A",
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}

	// Check that only items with "Price A" are returned
	for _, item := range result {
		if !contains(item.Name, "Price A") {
			t.Errorf("Expected item name to contain 'Price A', got '%s'", item.Name)
		}
	}
}

func TestBasicPriceUseCase_GetList_WithSearch_NoResults(t *testing.T) {
	// Test data
	mockData := []basicpriceDmn.BasicPrice{
		{
			ID:    1,
			Title: "Price AA",
		},
		{
			ID:    2,
			Title: "Price AB",
		},
	}

	mockDomain := &mockBasicPriceDomain{
		basicPrices: mockData,
		err:         nil,
	}

	uc := &BasicPriceUseCase{
		basicPrice: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "NonExistent",
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results, got %d", len(result))
	}
}

func TestBasicPriceUseCase_GetList_EmptyData(t *testing.T) {
	mockDomain := &mockBasicPriceDomain{
		basicPrices: []basicpriceDmn.BasicPrice{},
		err:         nil,
	}

	uc := &BasicPriceUseCase{
		basicPrice: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results, got %d", len(result))
	}
}

func TestBasicPriceUseCase_GetList_DomainError(t *testing.T) {
	expectedError := errors.New("database connection failed")

	mockDomain := &mockBasicPriceDomain{
		basicPrices: nil,
		err:         expectedError,
	}

	uc := &BasicPriceUseCase{
		basicPrice: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err == nil {
		t.Error("Expected error, got nil")
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results on error, got %d", len(result))
	}
}

func TestBasicPriceUseCase_GetList_ResponseMapping(t *testing.T) {
	// Test that Title field is correctly mapped to Name in response
	mockData := []basicpriceDmn.BasicPrice{
		{
			ID:          1,
			Code:        "BP001",
			Title:       "Basic Price Pattern 1",
			Explanation: "Test explanation",
			PriceJson:   `[{"hour":"00:00","price":100}]`,
		},
	}

	mockDomain := &mockBasicPriceDomain{
		basicPrices: mockData,
		err:         nil,
	}

	uc := &BasicPriceUseCase{
		basicPrice: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 1 {
		t.Errorf("Expected 1 result, got %d", len(result))
	}

	// Check that Title is mapped to Name
	if result[0].Name != "Basic Price Pattern 1" {
		t.Errorf("Expected name 'Basic Price Pattern 1', got '%s'", result[0].Name)
	}

	// Check that ID is preserved
	if result[0].ID != 1 {
		t.Errorf("Expected ID 1, got %d", result[0].ID)
	}
}

func TestBasicPriceUseCase_GetList_WithSearchEmptyString(t *testing.T) {
	// Test with empty search string - should return all results
	mockData := []basicpriceDmn.BasicPrice{
		{
			ID:    1,
			Title: "Price AA",
		},
		{
			ID:    2,
			Title: "Price BB",
		},
	}

	mockDomain := &mockBasicPriceDomain{
		basicPrices: mockData,
		err:         nil,
	}

	uc := &BasicPriceUseCase{
		basicPrice: mockDomain,
	}

	req := GetListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "", // Empty search
		},
	}

	result, err := uc.GetList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}
}
