package customer

import (
	customerDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/customer"
	departmentDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/department"
	departmentpicDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/department_pic"
)

type CustomerUseCase struct {
	customer      customerDmn.CustomerDomainItf
	department    departmentDmn.DepartmentDomainItf
	departmentPic departmentpicDmn.DepartmentPicDomainItf
}

type Domains struct {
	CustomerDomain      customerDmn.CustomerDomainItf
	DepartmentDomain    departmentDmn.DepartmentDomainItf
	DepartmentPicDomain departmentpicDmn.DepartmentPicDomainItf
}

func InitCustomerUseCase(d Domains) *CustomerUseCase {
	uc := &CustomerUseCase{
		customer:      d.CustomerDomain,
		department:    d.DepartmentDomain,
		departmentPic: d.DepartmentPicDomain,
	}
	return uc
}
