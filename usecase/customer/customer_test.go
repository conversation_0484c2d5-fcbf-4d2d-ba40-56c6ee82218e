package customer

import (
	"context"
	"errors"
	"testing"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	"github.com/Sera-Global/be-nbs-accounting-system/domain/customer"
	"github.com/Sera-Global/be-nbs-accounting-system/domain/department"
	departmentpic "github.com/Sera-Global/be-nbs-accounting-system/domain/department_pic"
)

// MockDepartmentPicDomain is a mock implementation of DepartmentPicDomainItf
type MockDepartmentPicDomain struct {
	GetListByDepartmentIDFunc func(ctx context.Context, param departmentpic.GetListByDepartmentIDParam) ([]departmentpic.DepartmentPic, error)
}

func (m *MockDepartmentPicDomain) GetListByDepartmentID(ctx context.Context, param departmentpic.GetListByDepartmentIDParam) ([]departmentpic.DepartmentPic, error) {
	if m.GetListByDepartmentIDFunc != nil {
		return m.GetListByDepartmentIDFunc(ctx, param)
	}
	return []departmentpic.DepartmentPic{}, nil
}

func TestCustomerUseCase_GetDepartmentPicList(t *testing.T) {
	tests := []struct {
		name           string
		req            GetDepartmentPicListReq
		mockResponse   []departmentpic.DepartmentPic
		mockError      error
		expectedResult []GetDepartmentPicListResp
		expectedError  bool
	}{
		{
			name: "Successful retrieval with multiple PICs",
			req: GetDepartmentPicListReq{
				DepartmentID: 1,
				BasicGetParam: types.BasicGetParam{
					Search: "",
				},
			},
			mockResponse: []departmentpic.DepartmentPic{
				{
					ID:      1,
					PicName: "John Doe",
				},
				{
					ID:      2,
					PicName: "John Wick",
				},
			},
			expectedResult: []GetDepartmentPicListResp{
				{
					ID:   1,
					Name: "John Doe",
				},
				{
					ID:   2,
					Name: "John Wick",
				},
			},
			expectedError: false,
		},
		{
			name: "Successful retrieval with search filter",
			req: GetDepartmentPicListReq{
				DepartmentID: 1,
				BasicGetParam: types.BasicGetParam{
					Search: "John",
				},
			},
			mockResponse: []departmentpic.DepartmentPic{
				{
					ID:      1,
					PicName: "John Doe",
				},
			},
			expectedResult: []GetDepartmentPicListResp{
				{
					ID:   1,
					Name: "John Doe",
				},
			},
			expectedError: false,
		},
		{
			name: "Empty result set",
			req: GetDepartmentPicListReq{
				DepartmentID: 1,
			},
			mockResponse:   []departmentpic.DepartmentPic{},
			expectedResult: []GetDepartmentPicListResp{},
			expectedError:  false,
		},
		{
			name: "Domain layer error",
			req: GetDepartmentPicListReq{
				DepartmentID: 1,
			},
			mockError:      errors.New("database connection error"),
			expectedResult: []GetDepartmentPicListResp{},
			expectedError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock domain
			mockDepartmentPic := &MockDepartmentPicDomain{
				GetListByDepartmentIDFunc: func(ctx context.Context, param departmentpic.GetListByDepartmentIDParam) ([]departmentpic.DepartmentPic, error) {
					// Verify the parameters are passed correctly
					if param.DepartmentID != tt.req.DepartmentID {
						t.Errorf("Expected DepartmentID %d, got %d", tt.req.DepartmentID, param.DepartmentID)
					}
					if param.Search != tt.req.Search {
						t.Errorf("Expected Search %s, got %s", tt.req.Search, param.Search)
					}

					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return tt.mockResponse, nil
				},
			}

			// Create usecase with mock
			uc := &CustomerUseCase{
				departmentPic: mockDepartmentPic,
			}

			// Execute the function
			result, err := uc.GetDepartmentPicList(context.Background(), tt.req)

			// Verify error expectation
			if tt.expectedError && err == nil {
				t.Errorf("Expected error but got none")
			}
			if !tt.expectedError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}

			// Verify result length
			if len(result) != len(tt.expectedResult) {
				t.Errorf("Expected %d results, got %d", len(tt.expectedResult), len(result))
			}

			// Verify each result item
			for i, expected := range tt.expectedResult {
				if i >= len(result) {
					break
				}
				if result[i].ID != expected.ID {
					t.Errorf("Expected ID %d at index %d, got %d", expected.ID, i, result[i].ID)
				}
				if result[i].Name != expected.Name {
					t.Errorf("Expected Name %s at index %d, got %s", expected.Name, i, result[i].Name)
				}
			}
		})
	}
}

func TestGetDepartmentPicListReq_ParameterMapping(t *testing.T) {
	// Test that request parameters are correctly mapped
	req := GetDepartmentPicListReq{
		DepartmentID: 123,
		BasicGetParam: types.BasicGetParam{
			Search:     "test search",
			SortBy:     "name",
			OrderBy:    "asc",
			PageNumber: 1,
			PageSize:   10,
		},
	}

	// Verify all fields are accessible
	if req.DepartmentID != 123 {
		t.Errorf("Expected DepartmentID 123, got %d", req.DepartmentID)
	}
	if req.Search != "test search" {
		t.Errorf("Expected Search 'test search', got %s", req.Search)
	}
	if req.SortBy != "name" {
		t.Errorf("Expected SortBy 'name', got %s", req.SortBy)
	}
	if req.OrderBy != "asc" {
		t.Errorf("Expected OrderBy 'asc', got %s", req.OrderBy)
	}
	if req.PageNumber != 1 {
		t.Errorf("Expected PageNumber 1, got %d", req.PageNumber)
	}
	if req.PageSize != 10 {
		t.Errorf("Expected PageSize 10, got %d", req.PageSize)
	}
}

func TestGetDepartmentPicListResp_JSONSerialization(t *testing.T) {
	// Test that response can be properly serialized to JSON
	resp := GetDepartmentPicListResp{
		ID:   42,
		Name: "Test PIC Name",
	}

	// This test ensures the struct tags are correct for JSON serialization
	// The actual JSON marshaling is tested in the HTTP layer tests
	if resp.ID != 42 {
		t.Errorf("Expected ID 42, got %d", resp.ID)
	}
	if resp.Name != "Test PIC Name" {
		t.Errorf("Expected Name 'Test PIC Name', got %s", resp.Name)
	}
}

// Mock implementations for customer and department domains
type MockCustomerDomain struct {
	GetListFunc func(ctx context.Context, param customer.GetListReq) ([]customer.Customer, error)
}

func (m *MockCustomerDomain) GetList(ctx context.Context, param customer.GetListReq) ([]customer.Customer, error) {
	if m.GetListFunc != nil {
		return m.GetListFunc(ctx, param)
	}
	return []customer.Customer{}, nil
}

type MockDepartmentDomain struct {
	GetListByCustomerIDFunc func(ctx context.Context, param department.GetListByCustomerIDParam) ([]department.Department, error)
}

func (m *MockDepartmentDomain) GetListByCustomerID(ctx context.Context, param department.GetListByCustomerIDParam) ([]department.Department, error) {
	if m.GetListByCustomerIDFunc != nil {
		return m.GetListByCustomerIDFunc(ctx, param)
	}
	return []department.Department{}, nil
}

func TestCustomerUseCase_GetList(t *testing.T) {
	tests := []struct {
		name           string
		req            GetListReq
		mockResponse   []customer.Customer
		mockError      error
		expectedResult []GetListResp
		expectedError  bool
	}{
		{
			name: "Successful retrieval",
			req: GetListReq{
				BasicGetParam: types.BasicGetParam{Search: "test"},
			},
			mockResponse: []customer.Customer{
				{ID: 1, Name: "Customer 1"},
				{ID: 2, Name: "Customer 2"},
			},
			expectedResult: []GetListResp{
				{ID: 1, Name: "Customer 1"},
				{ID: 2, Name: "Customer 2"},
			},
			expectedError: false,
		},
		{
			name:           "Empty result",
			req:            GetListReq{},
			mockResponse:   []customer.Customer{},
			expectedResult: []GetListResp{},
			expectedError:  false,
		},
		{
			name:           "Domain error",
			req:            GetListReq{},
			mockError:      errors.New("domain error"),
			expectedResult: []GetListResp{},
			expectedError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCustomer := &MockCustomerDomain{
				GetListFunc: func(ctx context.Context, param customer.GetListReq) ([]customer.Customer, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return tt.mockResponse, nil
				},
			}

			uc := &CustomerUseCase{customer: mockCustomer}
			result, err := uc.GetList(context.Background(), tt.req)

			if tt.expectedError && err == nil {
				t.Errorf("Expected error but got none")
			}
			if !tt.expectedError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
			if len(result) != len(tt.expectedResult) {
				t.Errorf("Expected %d results, got %d", len(tt.expectedResult), len(result))
			}
		})
	}
}

func TestCustomerUseCase_GetDepartmentList(t *testing.T) {
	tests := []struct {
		name           string
		req            GetDepartmentListReq
		mockResponse   []department.Department
		mockError      error
		expectedResult []GetDepartmentListResp
		expectedError  bool
	}{
		{
			name: "Successful retrieval",
			req: GetDepartmentListReq{
				CustomerID:    1,
				BasicGetParam: types.BasicGetParam{Search: "test"},
			},
			mockResponse: []department.Department{
				{ID: 1, Name: "Department 1"},
				{ID: 2, Name: "Department 2"},
			},
			expectedResult: []GetDepartmentListResp{
				{ID: 1, Name: "Department 1"},
				{ID: 2, Name: "Department 2"},
			},
			expectedError: false,
		},
		{
			name: "Empty result",
			req: GetDepartmentListReq{
				CustomerID: 1,
			},
			mockResponse:   []department.Department{},
			expectedResult: []GetDepartmentListResp{},
			expectedError:  false,
		},
		{
			name: "Domain error",
			req: GetDepartmentListReq{
				CustomerID: 1,
			},
			mockError:      errors.New("domain error"),
			expectedResult: []GetDepartmentListResp{},
			expectedError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDepartment := &MockDepartmentDomain{
				GetListByCustomerIDFunc: func(ctx context.Context, param department.GetListByCustomerIDParam) ([]department.Department, error) {
					if param.CustomerID != tt.req.CustomerID {
						t.Errorf("Expected CustomerID %d, got %d", tt.req.CustomerID, param.CustomerID)
					}
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return tt.mockResponse, nil
				},
			}

			uc := &CustomerUseCase{department: mockDepartment}
			result, err := uc.GetDepartmentList(context.Background(), tt.req)

			if tt.expectedError && err == nil {
				t.Errorf("Expected error but got none")
			}
			if !tt.expectedError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
			if len(result) != len(tt.expectedResult) {
				t.Errorf("Expected %d results, got %d", len(tt.expectedResult), len(result))
			}
		})
	}
}
