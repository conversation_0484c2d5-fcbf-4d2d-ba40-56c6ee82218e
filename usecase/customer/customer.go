package customer

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/domain/customer"
	"github.com/Sera-Global/be-nbs-accounting-system/domain/department"
	departmentpic "github.com/Sera-Global/be-nbs-accounting-system/domain/department_pic"
)

func (uc *CustomerUseCase) GetList(ctx context.Context, req GetListReq) ([]GetListResp, error) {
	// Get customers from domain
	customers, err := uc.customer.GetList(ctx, customer.GetListReq{
		BasicGetParam: req.BasicGetParam,
	})
	if err != nil {
		return []GetListResp{}, log.LogError(err, nil)
	}
	if len(customers) == 0 {
		return []GetListResp{}, nil
	}

	// Convert to response format
	var resp []GetListResp
	for _, customer := range customers {
		resp = append(resp, GetListResp{
			ID:   customer.ID,
			Name: customer.Name,
		})
	}

	return resp, nil
}

func (uc *CustomerUseCase) GetDepartmentList(ctx context.Context, req GetDepartmentListReq) ([]GetDepartmentListResp, error) {
	// Get departments from domain
	departments, err := uc.department.GetListByCustomerID(ctx, department.GetListByCustomerIDParam{
		CustomerID:    req.CustomerID,
		BasicGetParam: req.BasicGetParam,
	})
	if err != nil {
		return []GetDepartmentListResp{}, log.LogError(err, nil)
	}
	if len(departments) == 0 {
		return []GetDepartmentListResp{}, nil
	}

	// Convert to response format
	var resp []GetDepartmentListResp
	for _, department := range departments {
		resp = append(resp, GetDepartmentListResp{
			ID:   department.ID,
			Name: department.Name,
		})
	}

	return resp, nil
}

func (uc *CustomerUseCase) GetDepartmentPicList(ctx context.Context, req GetDepartmentPicListReq) ([]GetDepartmentPicListResp, error) {
	// Get department PICs from domain
	departmentPics, err := uc.departmentPic.GetListByDepartmentID(ctx, departmentpic.GetListByDepartmentIDParam{
		DepartmentID:  req.DepartmentID,
		BasicGetParam: req.BasicGetParam,
	})
	if err != nil {
		return []GetDepartmentPicListResp{}, log.LogError(err, nil)
	}
	if len(departmentPics) == 0 {
		return []GetDepartmentPicListResp{}, nil
	}

	// Convert to response format
	var resp []GetDepartmentPicListResp
	for _, departmentPic := range departmentPics {
		resp = append(resp, GetDepartmentPicListResp{
			ID:   departmentPic.ID,
			Name: departmentPic.PicName,
		})
	}

	return resp, nil
}
