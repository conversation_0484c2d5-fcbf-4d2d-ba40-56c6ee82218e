package sitereport

import (
	"context"
	"errors"
	"testing"
	"time"

	basicpriceDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
	dailyreportadditionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/daily_report_addition"
	statutoryDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/statutory"
)

// Mock domains for testing
type mockStatutoryDomain struct {
	statutory statutoryDmn.Statutory
	err       error
}

func (m *mockStatutoryDomain) GetList(ctx context.Context, param statutoryDmn.GetListReq) ([]statutoryDmn.Statutory, error) {
	return []statutoryDmn.Statutory{}, nil
}

func (m *mockStatutoryDomain) GetByCustomerID(ctx context.Context, param statutoryDmn.GetByCustomerIDParam) (statutoryDmn.Statutory, error) {
	if m.err != nil {
		return statutoryDmn.Statutory{}, m.err
	}
	return m.statutory, nil
}

type mockBasicPriceDomainForCalc struct {
	basicPrice basicpriceDmn.BasicPrice
	err        error
}

func (m *mockBasicPriceDomainForCalc) GetList(ctx context.Context, param basicpriceDmn.GetListReq) ([]basicpriceDmn.BasicPrice, error) {
	return []basicpriceDmn.BasicPrice{}, nil
}

func (m *mockBasicPriceDomainForCalc) GetByID(ctx context.Context, param basicpriceDmn.GetByIDParam) (basicpriceDmn.BasicPrice, error) {
	if m.err != nil {
		return basicpriceDmn.BasicPrice{}, m.err
	}
	return m.basicPrice, nil
}

type mockDailyReportAdditionDomain struct {
	addition dailyreportadditionDmn.DailyReportAddition
	err      error
}

func (m *mockDailyReportAdditionDomain) GetList(ctx context.Context, param dailyreportadditionDmn.GetListReq) ([]dailyreportadditionDmn.DailyReportAddition, error) {
	return []dailyreportadditionDmn.DailyReportAddition{}, nil
}

func (m *mockDailyReportAdditionDomain) GetByID(ctx context.Context, param dailyreportadditionDmn.GetByIDParam) (dailyreportadditionDmn.DailyReportAddition, error) {
	if m.err != nil {
		return dailyreportadditionDmn.DailyReportAddition{}, m.err
	}
	return m.addition, nil
}

func TestSiteReportUseCase_CalculateStatutoryRate_Success(t *testing.T) {
	mockStatutory := &mockStatutoryDomain{
		statutory: statutoryDmn.Statutory{
			ID:   1,
			Rate: 12.5,
		},
		err: nil,
	}

	uc := &SiteReportUseCase{
		statutory: mockStatutory,
	}

	rate, err := uc.calculateStatutoryRate(context.Background(), 1)

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if rate != 12.5 {
		t.Errorf("Expected rate 12.5, got %f", rate)
	}
}

func TestSiteReportUseCase_CalculateStatutoryRate_NotFound(t *testing.T) {
	mockStatutory := &mockStatutoryDomain{
		statutory: statutoryDmn.Statutory{}, // Empty statutory (ID = 0)
		err:       nil,
	}

	uc := &SiteReportUseCase{
		statutory: mockStatutory,
	}

	_, err := uc.calculateStatutoryRate(context.Background(), 1)

	if err == nil {
		t.Error("Expected error for not found statutory, got nil")
	}

	expectedError := "法定が見つかりません"
	if err.Error() != expectedError {
		t.Errorf("Expected error '%s', got '%s'", expectedError, err.Error())
	}
}

func TestSiteReportUseCase_CalculateStatutoryRate_Error(t *testing.T) {
	mockStatutory := &mockStatutoryDomain{
		statutory: statutoryDmn.Statutory{},
		err:       errors.New("database error"),
	}

	uc := &SiteReportUseCase{
		statutory: mockStatutory,
	}

	_, err := uc.calculateStatutoryRate(context.Background(), 1)

	if err == nil {
		t.Error("Expected error, got nil")
	}
}

func TestSiteReportUseCase_CalculateExpensePerWorker_Success(t *testing.T) {
	priceJSON := `[
		{"hour":"09:00","price":1000},
		{"hour":"10:00","price":1200},
		{"hour":"11:00","price":1100},
		{"hour":"12:00","price":1300},
		{"hour":"13:00","price":1150},
		{"hour":"14:00","price":1250},
		{"hour":"15:00","price":1050},
		{"hour":"16:00","price":1400}
	]`

	mockBasicPrice := &mockBasicPriceDomainForCalc{
		basicPrice: basicpriceDmn.BasicPrice{
			ID:        1,
			PriceJson: priceJSON,
		},
		err: nil,
	}

	uc := &SiteReportUseCase{
		basicPrice: mockBasicPrice,
	}

	startTime, _ := time.Parse("15:04:05", "09:00:00")
	endTime, _ := time.Parse("15:04:05", "17:00:00")
	breakTime, _ := time.Parse("15:04:05", "12:00:00")

	expense, err := uc.calculateExpensePerWorker(context.Background(), CalculateExpensePerWorker{
		BasicPriceID: 1,
		StartTime:    startTime,
		EndTime:      endTime,
		BreakTime:    &breakTime,
	})

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Expected: 1000 + 1200 + 1100 + 1150 + 1250 + 1050 + 1400 = 8150 (excluding 12:00)
	expected := float64(8150)
	if expense != expected {
		t.Errorf("Expected expense %f, got %f", expected, expense)
	}
}

func TestSiteReportUseCase_CalculateExpensePerWorker_NoBreak(t *testing.T) {
	priceJSON := `[
		{"hour":"09:00","price":1000},
		{"hour":"10:00","price":1200},
		{"hour":"11:00","price":1100}
	]`

	mockBasicPrice := &mockBasicPriceDomainForCalc{
		basicPrice: basicpriceDmn.BasicPrice{
			ID:        1,
			PriceJson: priceJSON,
		},
		err: nil,
	}

	uc := &SiteReportUseCase{
		basicPrice: mockBasicPrice,
	}

	startTime, _ := time.Parse("15:04:05", "09:00:00")
	endTime, _ := time.Parse("15:04:05", "12:00:00")

	expense, err := uc.calculateExpensePerWorker(context.Background(), CalculateExpensePerWorker{
		BasicPriceID: 1,
		StartTime:    startTime,
		EndTime:      endTime,
		BreakTime:    nil,
	})

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Expected: 1000 + 1200 + 1100 = 3300
	expected := float64(3300)
	if expense != expected {
		t.Errorf("Expected expense %f, got %f", expected, expense)
	}
}

func TestSiteReportUseCase_CalculateExpensePerWorker_InvalidJSON(t *testing.T) {
	mockBasicPrice := &mockBasicPriceDomainForCalc{
		basicPrice: basicpriceDmn.BasicPrice{
			ID:        1,
			PriceJson: "invalid json",
		},
		err: nil,
	}

	uc := &SiteReportUseCase{
		basicPrice: mockBasicPrice,
	}

	startTime, _ := time.Parse("15:04:05", "09:00:00")
	endTime, _ := time.Parse("15:04:05", "17:00:00")

	_, err := uc.calculateExpensePerWorker(context.Background(), CalculateExpensePerWorker{
		BasicPriceID: 1,
		StartTime:    startTime,
		EndTime:      endTime,
		BreakTime:    nil,
	})

	if err == nil {
		t.Error("Expected error for invalid JSON, got nil")
	}
}

func TestSiteReportUseCase_CalculateExpensePerWorker_NotFound(t *testing.T) {
	mockBasicPrice := &mockBasicPriceDomainForCalc{
		basicPrice: basicpriceDmn.BasicPrice{}, // Empty basic price (ID = 0)
		err:        nil,
	}

	uc := &SiteReportUseCase{
		basicPrice: mockBasicPrice,
	}

	startTime, _ := time.Parse("15:04:05", "09:00:00")
	endTime, _ := time.Parse("15:04:05", "17:00:00")

	_, err := uc.calculateExpensePerWorker(context.Background(), CalculateExpensePerWorker{
		BasicPriceID: 1,
		StartTime:    startTime,
		EndTime:      endTime,
		BreakTime:    nil,
	})

	if err == nil {
		t.Error("Expected error for not found basic price, got nil")
	}

	expectedError := "契約タイプが見つかりません"
	if err.Error() != expectedError {
		t.Errorf("Expected error '%s', got '%s'", expectedError, err.Error())
	}
}

func TestSiteReportUseCase_CalculateAddFees_Success(t *testing.T) {
	additionID := int64(1)
	mockAddition := &mockDailyReportAdditionDomain{
		addition: dailyreportadditionDmn.DailyReportAddition{
			ID:            1,
			AmountPerSite: 2000,
			AmountPerHour: 1000,
		},
		err: nil,
	}

	uc := &SiteReportUseCase{
		dailyReportAddition: mockAddition,
	}

	addFeePerSite, addFeePerWorker, err := uc.calculateAddFees(context.Background(), &additionID)

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if addFeePerSite != 2000 {
		t.Errorf("Expected addFeePerSite 2000, got %f", addFeePerSite)
	}

	if addFeePerWorker != 1000 {
		t.Errorf("Expected addFeePerWorker 1000, got %f", addFeePerWorker)
	}
}

func TestSiteReportUseCase_CalculateAddFees_NilID(t *testing.T) {
	uc := &SiteReportUseCase{}

	addFeePerSite, addFeePerWorker, err := uc.calculateAddFees(context.Background(), nil)

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if addFeePerSite != 0 {
		t.Errorf("Expected addFeePerSite 0, got %f", addFeePerSite)
	}

	if addFeePerWorker != 0 {
		t.Errorf("Expected addFeePerWorker 0, got %f", addFeePerWorker)
	}
}

func TestSiteReportUseCase_CalculateAddFees_NotFound(t *testing.T) {
	additionID := int64(1)
	mockAddition := &mockDailyReportAdditionDomain{
		addition: dailyreportadditionDmn.DailyReportAddition{}, // Empty addition (ID = 0)
		err:      nil,
	}

	uc := &SiteReportUseCase{
		dailyReportAddition: mockAddition,
	}

	_, _, err := uc.calculateAddFees(context.Background(), &additionID)

	if err == nil {
		t.Error("Expected error for not found daily report addition, got nil")
	}

	expectedError := "日報追加が見つかりません"
	if err.Error() != expectedError {
		t.Errorf("Expected error '%s', got '%s'", expectedError, err.Error())
	}
}

func TestSiteReportUseCase_GetStatutoryCalculationVariable_Success(t *testing.T) {
	priceJSON := `[
		{"hour":"09:00","price":1000},
		{"hour":"10:00","price":1200},
		{"hour":"11:00","price":1100},
		{"hour":"12:00","price":1300},
		{"hour":"13:00","price":1150}
	]`

	additionID := int64(1)

	mockStatutory := &mockStatutoryDomain{
		statutory: statutoryDmn.Statutory{
			ID:   1,
			Rate: 12.5,
		},
		err: nil,
	}

	mockBasicPrice := &mockBasicPriceDomainForCalc{
		basicPrice: basicpriceDmn.BasicPrice{
			ID:        1,
			PriceJson: priceJSON,
		},
		err: nil,
	}

	mockAddition := &mockDailyReportAdditionDomain{
		addition: dailyreportadditionDmn.DailyReportAddition{
			ID:            1,
			AmountPerSite: 2000,
			AmountPerHour: 1000,
		},
		err: nil,
	}

	uc := &SiteReportUseCase{
		statutory:           mockStatutory,
		basicPrice:          mockBasicPrice,
		dailyReportAddition: mockAddition,
	}

	startTime, _ := time.Parse("15:04:05", "09:00:00")
	endTime, _ := time.Parse("15:04:05", "14:00:00")
	breakTime, _ := time.Parse("15:04:05", "12:00:00")

	req := GetStatutoryCalculationVariableReq{
		CustomerID:            1,
		BasicPriceID:          1,
		DailyReportAdditionID: &additionID,
		ParsedStartTime:       startTime,
		ParsedEndTime:         endTime,
		ParsedBreakTime:       &breakTime,
	}

	resp, err := uc.GetStatutoryCalculationVariable(context.Background(), req)

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if resp.StatutoryRate != 12.5 {
		t.Errorf("Expected StatutoryRate 12.5, got %f", resp.StatutoryRate)
	}

	// Expected expense: 1000 + 1200 + 1100 + 1150 = 4450 (excluding 12:00)
	if resp.ExpensePerWorker != 4450 {
		t.Errorf("Expected ExpensePerWorker 4450, got %f", resp.ExpensePerWorker)
	}

	if resp.AddFeePerSite != 2000 {
		t.Errorf("Expected AddFeePerSite 2000, got %f", resp.AddFeePerSite)
	}

	if resp.AddFeePerWorker != 1000 {
		t.Errorf("Expected AddFeePerWorker 1000, got %f", resp.AddFeePerWorker)
	}
}

func TestSiteReportUseCase_GetStatutoryCalculationVariable_NoAddition(t *testing.T) {
	priceJSON := `[
		{"hour":"09:00","price":1000},
		{"hour":"10:00","price":1200}
	]`

	mockStatutory := &mockStatutoryDomain{
		statutory: statutoryDmn.Statutory{
			ID:   1,
			Rate: 15.0,
		},
		err: nil,
	}

	mockBasicPrice := &mockBasicPriceDomainForCalc{
		basicPrice: basicpriceDmn.BasicPrice{
			ID:        1,
			PriceJson: priceJSON,
		},
		err: nil,
	}

	uc := &SiteReportUseCase{
		statutory:  mockStatutory,
		basicPrice: mockBasicPrice,
	}

	startTime, _ := time.Parse("15:04:05", "09:00:00")
	endTime, _ := time.Parse("15:04:05", "11:00:00")

	req := GetStatutoryCalculationVariableReq{
		CustomerID:            1,
		BasicPriceID:          1,
		DailyReportAdditionID: nil, // No addition ID
		ParsedStartTime:       startTime,
		ParsedEndTime:         endTime,
		ParsedBreakTime:       nil,
	}

	resp, err := uc.GetStatutoryCalculationVariable(context.Background(), req)

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if resp.StatutoryRate != 15.0 {
		t.Errorf("Expected StatutoryRate 15.0, got %f", resp.StatutoryRate)
	}

	// Expected expense: 1000 + 1200 = 2200
	if resp.ExpensePerWorker != 2200 {
		t.Errorf("Expected ExpensePerWorker 2200, got %f", resp.ExpensePerWorker)
	}

	if resp.AddFeePerSite != 0 {
		t.Errorf("Expected AddFeePerSite 0, got %f", resp.AddFeePerSite)
	}

	if resp.AddFeePerWorker != 0 {
		t.Errorf("Expected AddFeePerWorker 0, got %f", resp.AddFeePerWorker)
	}
}

func TestSiteReportUseCase_GetStatutoryCalculationVariable_StatutoryError(t *testing.T) {
	mockStatutory := &mockStatutoryDomain{
		statutory: statutoryDmn.Statutory{},
		err:       errors.New("statutory error"),
	}

	// Need to provide all dependencies since implementation uses goroutines
	mockBasicPrice := &mockBasicPriceDomainForCalc{
		basicPrice: basicpriceDmn.BasicPrice{
			ID:        1,
			PriceJson: `[{"hour":"09:00","price":1000}]`,
		},
		err: nil,
	}

	mockAddition := &mockDailyReportAdditionDomain{
		addition: dailyreportadditionDmn.DailyReportAddition{
			ID:            1,
			AmountPerSite: 2000,
			AmountPerHour: 1000,
		},
		err: nil,
	}

	uc := &SiteReportUseCase{
		statutory:           mockStatutory,
		basicPrice:          mockBasicPrice,
		dailyReportAddition: mockAddition,
	}

	startTime, _ := time.Parse("15:04:05", "09:00:00")
	endTime, _ := time.Parse("15:04:05", "10:00:00")

	req := GetStatutoryCalculationVariableReq{
		CustomerID:      1,
		BasicPriceID:    1,
		ParsedStartTime: startTime,
		ParsedEndTime:   endTime,
	}

	_, err := uc.GetStatutoryCalculationVariable(context.Background(), req)

	if err == nil {
		t.Error("Expected error, got nil")
	}
}

func TestSiteReportUseCase_GetStatutoryCalculationVariable_BasicPriceError(t *testing.T) {
	mockStatutory := &mockStatutoryDomain{
		statutory: statutoryDmn.Statutory{
			ID:   1,
			Rate: 12.5,
		},
		err: nil,
	}

	mockBasicPrice := &mockBasicPriceDomainForCalc{
		basicPrice: basicpriceDmn.BasicPrice{},
		err:        errors.New("basic price error"),
	}

	mockAddition := &mockDailyReportAdditionDomain{
		addition: dailyreportadditionDmn.DailyReportAddition{
			ID:            1,
			AmountPerSite: 2000,
			AmountPerHour: 1000,
		},
		err: nil,
	}

	uc := &SiteReportUseCase{
		statutory:           mockStatutory,
		basicPrice:          mockBasicPrice,
		dailyReportAddition: mockAddition,
	}

	startTime, _ := time.Parse("15:04:05", "09:00:00")
	endTime, _ := time.Parse("15:04:05", "17:00:00")

	req := GetStatutoryCalculationVariableReq{
		CustomerID:      1,
		BasicPriceID:    1,
		ParsedStartTime: startTime,
		ParsedEndTime:   endTime,
	}

	_, err := uc.GetStatutoryCalculationVariable(context.Background(), req)

	if err == nil {
		t.Error("Expected error, got nil")
	}
}

func TestSiteReportUseCase_GetStatutoryCalculationVariable_AdditionError(t *testing.T) {
	priceJSON := `[{"hour":"09:00","price":1000}]`
	additionID := int64(1)

	mockStatutory := &mockStatutoryDomain{
		statutory: statutoryDmn.Statutory{
			ID:   1,
			Rate: 12.5,
		},
		err: nil,
	}

	mockBasicPrice := &mockBasicPriceDomainForCalc{
		basicPrice: basicpriceDmn.BasicPrice{
			ID:        1,
			PriceJson: priceJSON,
		},
		err: nil,
	}

	mockAddition := &mockDailyReportAdditionDomain{
		addition: dailyreportadditionDmn.DailyReportAddition{},
		err:      errors.New("addition error"),
	}

	uc := &SiteReportUseCase{
		statutory:           mockStatutory,
		basicPrice:          mockBasicPrice,
		dailyReportAddition: mockAddition,
	}

	startTime, _ := time.Parse("15:04:05", "09:00:00")
	endTime, _ := time.Parse("15:04:05", "10:00:00")

	req := GetStatutoryCalculationVariableReq{
		CustomerID:            1,
		BasicPriceID:          1,
		DailyReportAdditionID: &additionID,
		ParsedStartTime:       startTime,
		ParsedEndTime:         endTime,
	}

	_, err := uc.GetStatutoryCalculationVariable(context.Background(), req)

	if err == nil {
		t.Error("Expected error, got nil")
	}
}
