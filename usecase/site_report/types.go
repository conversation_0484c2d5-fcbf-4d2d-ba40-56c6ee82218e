package sitereport

import "time"

// GetListReq represents the request parameters for getting site report list
type GetListReq struct {
	StartDate string `query:"start_date"`
	EndDate   string `query:"end_date"`

	ParsedStartDate time.Time
	ParsedEndDate   time.Time
}

// GetListResp represents the response structure for site report list
type GetListResp struct {
	Year  string      `json:"year"`
	Month []MonthData `json:"month"`
}

type MonthData struct {
	Value       string     `json:"value"`
	Worker      int64      `json:"worker"`
	TotalAmount float64    `json:"total_amount"`
	Date        []DateData `json:"date"`
}

type DateData struct {
	Value       string       `json:"value"`
	Worker      int64        `json:"worker"`
	TotalAmount float64      `json:"total_amount"`
	Report      []ReportData `json:"report"`
}

type ReportData struct {
	SiteReportID int64   `json:"site_report_id"`
	SiteName     string  `json:"site_name"`
	Worker       int64   `json:"worker"`
	TotalAmount  float64 `json:"total_amount"`
	BStartTime   string  `json:"b_start_time"`
	BEndTime     string  `json:"b_end_time"`
	Note         string  `json:"note"`
	IsLocked     bool    `json:"is_locked"`
}

// BulkUpdateReq represents the request structure for bulk updating site reports
type BulkUpdateReq struct {
	SiteReportIDs   []int64 `json:"site_report_ids"`
	WorkDate        *string `json:"work_date,omitempty"`         // Optional, format: YYYY-MM-DD
	IsLocked        *bool   `json:"is_locked,omitempty"`         // Optional boolean
	IsInvoiceIssued *bool   `json:"is_invoice_issued,omitempty"` // Optional boolean

	UserRoles []string
}

// GetStatutoryCalculationVariableReq represents the request parameters for getting statutory calculation variables
type GetStatutoryCalculationVariableReq struct {
	CustomerID            int64  `query:"customer_id"`
	BasicPriceID          int64  `query:"basic_price_id"`
	StartTime             string `query:"start_time"`
	EndTime               string `query:"end_time"`
	BreakTime             string `query:"break_time"`
	DailyReportAdditionID *int64 `query:"daily_report_addition_id"`

	// Parsed fields for internal use
	ParsedStartTime time.Time
	ParsedEndTime   time.Time
	ParsedBreakTime *time.Time
}

// GetStatutoryCalculationVariableResp represents the response structure for statutory calculation variables
type GetStatutoryCalculationVariableResp struct {
	StatutoryRate    float64 `json:"statutory_rate"`
	ExpensePerWorker float64 `json:"expense_per_worker"`
	AddFeePerSite    float64 `json:"add_fee_per_site"`
	AddFeePerWorker  float64 `json:"add_fee_per_worker"`
}

type PriceItem struct {
	Hour  string  `json:"hour"`
	Price float64 `json:"price"`
}

type CalculateExpensePerWorker struct {
	BasicPriceID int64
	StartTime    time.Time
	EndTime      time.Time
	BreakTime    *time.Time
}

// WorkerCalculationReq represents the request parameters for worker calculation
type WorkerCalculationReq struct {
	BasicPriceID              int64   `json:"basic_price_id"`
	StartTime                 string  `json:"start_time"`
	EndTime                   string  `json:"end_time"`
	BreakTime                 string  `json:"break_time"`
	TransportExpense          float64 `json:"transport_expense"`
	LeaderAllowance           float64 `json:"leader_allowance"`
	DistantFeeID              int64   `json:"distant_fee_id"`
	QualificationAllowanceIDs []int64 `json:"qualification_allowance_ids"`

	// Parsed fields for internal use
	ParsedStartTime time.Time
	ParsedEndTime   time.Time
	ParsedBreakTime *time.Time
}

// WorkerCalculationResp represents the response structure for worker calculation
type WorkerCalculationResp struct {
	TotalAmount         float64 `json:"total_amount"`
	TaxAmount           float64 `json:"tax_amount"`
	TotalAmountAfterTax float64 `json:"total_amount_after_tax"`
}

type DoWorkerCalculationResp struct {
	TotalAmount         float64
	TaxIncomeID         int64
	TaxAmount           float64
	TotalAmountAfterTax float64
	Qualifications      []Qualification
}

type CalculateQualificationFeeResp struct {
	TotalFee       float64
	Qualifications []Qualification
}

type Qualification struct {
	ID       int64
	Title    string
	AddClaim float64
}
