package sitereport

import (
	basicpriceDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
	dailyreportadditionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/daily_report_addition"
	distantfeeDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/distant_fee"
	incomeTaxDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/income_tax"
	qualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/qualification"
	sitereportDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report"
	statutoryDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/statutory"
)

type SiteReportUseCase struct {
	sitereport          sitereportDmn.SiteReportDomainItf
	basicPrice          basicpriceDmn.BasicPriceDomainItf
	statutory           statutoryDmn.StatutoryDomainItf
	dailyReportAddition dailyreportadditionDmn.DailyReportAdditionDomainItf
	distantfee          distantfeeDmn.DistantFeeDomainItf
	incomeTax           incomeTaxDmn.IncomeTaxDomainItf
	qualification       qualificationDmn.QualificationDomainItf
}

type Domains struct {
	SiteReportDomain          sitereportDmn.SiteReportDomainItf
	BasicPriceDomain          basicpriceDmn.BasicPriceDomainItf
	StatutoryDomain           statutoryDmn.StatutoryDomainItf
	DailyReportAdditionDomain dailyreportadditionDmn.DailyReportAdditionDomainItf
	DistantFeeDomain          distantfeeDmn.DistantFeeDomainItf
	IncomeTaxDomain           incomeTaxDmn.IncomeTaxDomainItf
	QualificationDomain       qualificationDmn.QualificationDomainItf
}

func InitSiteReportUseCase(d Domains) *SiteReportUseCase {
	uc := &SiteReportUseCase{
		sitereport:          d.SiteReportDomain,
		basicPrice:          d.BasicPriceDomain,
		statutory:           d.StatutoryDomain,
		dailyReportAddition: d.DailyReportAdditionDomain,
		distantfee:          d.DistantFeeDomain,
		incomeTax:           d.IncomeTaxDomain,
		qualification:       d.QualificationDomain,
	}
	return uc
}
