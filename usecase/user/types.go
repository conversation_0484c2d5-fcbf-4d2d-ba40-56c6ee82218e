package user

import "github.com/Sera-Global/be-nbs-accounting-system/common/types"

type LoginReq struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type LoginResp struct {
	ID       int64  `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
	Role     string `json:"role"`
	Token    string `json:"token"`
}

// GetWorkerListReq represents the request parameters for getting worker list
type GetWorkerListReq struct {
	types.BasicGetParam
}

// GetWorkerListResp represents the response structure for worker list
type GetWorkerListResp struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}
