package user

import (
	userDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user"
	userroleDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user_role"
)

type UserUseCase struct {
	user     userDmn.UserDomainItf
	userrole userroleDmn.UserRoleDomainItf
}

type Domains struct {
	UserDomain     userDmn.UserDomainItf
	UserRoleDomain userroleDmn.UserRoleDomainItf
}

func InitUserUseCase(d Domains) *UserUseCase {
	uc := &UserUseCase{
		user:     d.UserDomain,
		userrole: d.UserRoleDomain,
	}
	return uc
}
