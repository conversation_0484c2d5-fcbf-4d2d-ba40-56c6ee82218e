package user

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	userDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user"
)

func (uc *UserUseCase) GetWorkerList(ctx context.Context, req GetWorkerListReq) ([]GetWorkerListResp, error) {
	// Get workers from domain
	workers, err := uc.user.GetWorkerList(ctx, userDmn.GetWorkerListReq{
		BasicGetParam: req.BasicGetParam,
	})
	if err != nil {
		return []GetWorkerListResp{}, log.LogError(err, nil)
	}
	if len(workers) == 0 {
		return []GetWorkerListResp{}, nil
	}

	// Convert to response format
	var resp []GetWorkerListResp
	for _, worker := range workers {
		resp = append(resp, GetWorkerListResp{
			ID:   worker.ID,
			Name: worker.Name,
		})
	}

	return resp, nil
}
