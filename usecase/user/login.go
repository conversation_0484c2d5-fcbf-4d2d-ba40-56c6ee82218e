package user

import (
	"context"
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	userDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/config"
	"github.com/Sera-Global/be-nbs-accounting-system/server/http/middleware"
	"github.com/dgrijalva/jwt-go"
	"golang.org/x/crypto/bcrypt"
)

func (uc *UserUseCase) Login(ctx context.Context, req LoginReq) (LoginResp, error) {
	user, err := uc.user.GetByUsername(ctx, req.Username)
	if err != nil {
		return LoginResp{}, log.LogError(err, nil)
	}

	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password))
	if err != nil {
		return LoginResp{}, log.LogError(ErrInvalidCredentials, nil)
	}

	roles, err := uc.userrole.GetRolesByUserID(ctx, user.ID)
	if err != nil {
		return LoginResp{}, log.LogError(err, nil)
	}

	// Generate JWT token
	tokenStr, err := generateJWTToken(user, roles)
	if err != nil {
		return LoginResp{}, log.LogError(err, nil)
	}

	role := ""
	if len(roles) > 0 {
		role = roles[0]
	}

	resp := LoginResp{
		ID:       user.ID,
		Username: user.Username,
		Name:     user.Name,
		Role:     role,
		Token:    tokenStr,
	}

	return resp, nil
}

// generateJWTToken creates a JWT token string for a user and their roles.
func generateJWTToken(user userDmn.User, roles []string) (string, error) {
	now := time.Now()
	cfg := config.GetConfig()
	claims := middleware.JWTPayload{
		ID:       user.ID,
		Username: user.Username,
		Name:     user.Name,
		Roles:    roles,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: now.Add(720 * time.Hour).Unix(),
			IssuedAt:  now.Unix(),
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(cfg.JWTSecret))
}
