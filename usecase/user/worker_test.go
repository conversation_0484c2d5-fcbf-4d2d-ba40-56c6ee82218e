package user

import (
	"context"
	"errors"
	"strings"
	"testing"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	userDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user"
)

// Mock implementation of UserDomainItf for testing
type mockUserDomain struct {
	workers []userDmn.WorkerListItem
	err     error
}

func (m *mockUserDomain) GetByUsername(ctx context.Context, username string) (userDmn.User, error) {
	return userDmn.User{}, nil
}

func (m *mockUserDomain) GetLoginUser(ctx context.Context, param userDmn.User) error {
	return nil
}

func (m *mockUserDomain) GetWorkerList(ctx context.Context, param userDmn.GetWorkerListReq) ([]userDmn.WorkerListItem, error) {
	if m.err != nil {
		return nil, m.err
	}

	// Filter by search if provided
	if param.Search != "" {
		var filtered []userDmn.WorkerListItem
		for _, worker := range m.workers {
			// Case-insensitive search simulation
			if strings.Contains(strings.ToLower(worker.Name), strings.ToLower(param.Search)) {
				filtered = append(filtered, worker)
			}
		}
		return filtered, nil
	}

	return m.workers, nil
}

func TestUserUseCase_GetWorkerList_Success(t *testing.T) {
	// Test data
	mockData := []userDmn.WorkerListItem{
		{
			ID:   1,
			Name: "John Doe",
		},
		{
			ID:   2,
			Name: "Jane Smith",
		},
		{
			ID:   3,
			Name: "Bob Johnson",
		},
	}

	mockDomain := &mockUserDomain{
		workers: mockData,
		err:     nil,
	}

	uc := &UserUseCase{
		user: mockDomain,
	}

	req := GetWorkerListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetWorkerList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 3 {
		t.Errorf("Expected 3 results, got %d", len(result))
	}

	// Check first item
	if result[0].ID != 1 {
		t.Errorf("Expected ID 1, got %d", result[0].ID)
	}

	if result[0].Name != "John Doe" {
		t.Errorf("Expected name 'John Doe', got %s", result[0].Name)
	}
}

func TestUserUseCase_GetWorkerList_WithSearch_Success(t *testing.T) {
	// Test data
	mockData := []userDmn.WorkerListItem{
		{
			ID:   1,
			Name: "John Doe",
		},
		{
			ID:   2,
			Name: "John Wick",
		},
		{
			ID:   3,
			Name: "Jane Smith",
		},
	}

	mockDomain := &mockUserDomain{
		workers: mockData,
		err:     nil,
	}

	uc := &UserUseCase{
		user: mockDomain,
	}

	req := GetWorkerListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "john",
		},
	}

	result, err := uc.GetWorkerList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}

	// Check that both Johns are returned
	names := make(map[string]bool)
	for _, worker := range result {
		names[worker.Name] = true
	}

	if !names["John Doe"] {
		t.Error("Expected John Doe in results")
	}

	if !names["John Wick"] {
		t.Error("Expected John Wick in results")
	}
}

func TestUserUseCase_GetWorkerList_WithSearch_NoResults(t *testing.T) {
	// Test data
	mockData := []userDmn.WorkerListItem{
		{
			ID:   1,
			Name: "John Doe",
		},
		{
			ID:   2,
			Name: "Jane Smith",
		},
	}

	mockDomain := &mockUserDomain{
		workers: mockData,
		err:     nil,
	}

	uc := &UserUseCase{
		user: mockDomain,
	}

	req := GetWorkerListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "NonExistent",
		},
	}

	result, err := uc.GetWorkerList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results, got %d", len(result))
	}
}

func TestUserUseCase_GetWorkerList_EmptyData(t *testing.T) {
	mockDomain := &mockUserDomain{
		workers: []userDmn.WorkerListItem{},
		err:     nil,
	}

	uc := &UserUseCase{
		user: mockDomain,
	}

	req := GetWorkerListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetWorkerList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results, got %d", len(result))
	}
}

func TestUserUseCase_GetWorkerList_DomainError(t *testing.T) {
	expectedError := errors.New("database connection failed")

	mockDomain := &mockUserDomain{
		workers: nil,
		err:     expectedError,
	}

	uc := &UserUseCase{
		user: mockDomain,
	}

	req := GetWorkerListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetWorkerList(context.Background(), req)

	// Assertions
	if err == nil {
		t.Error("Expected error, got nil")
	}

	if len(result) != 0 {
		t.Errorf("Expected 0 results on error, got %d", len(result))
	}
}

func TestUserUseCase_GetWorkerList_ResponseMapping(t *testing.T) {
	// Test that fields are correctly mapped in response
	mockData := []userDmn.WorkerListItem{
		{
			ID:   123,
			Name: "Test Worker",
		},
	}

	mockDomain := &mockUserDomain{
		workers: mockData,
		err:     nil,
	}

	uc := &UserUseCase{
		user: mockDomain,
	}

	req := GetWorkerListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetWorkerList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 1 {
		t.Errorf("Expected 1 result, got %d", len(result))
	}

	// Check field mapping
	if result[0].ID != 123 {
		t.Errorf("Expected ID 123, got %d", result[0].ID)
	}

	if result[0].Name != "Test Worker" {
		t.Errorf("Expected name 'Test Worker', got %s", result[0].Name)
	}
}

func TestUserUseCase_GetWorkerList_WithSearchEmptyString(t *testing.T) {
	// Test with empty search string - should return all results
	mockData := []userDmn.WorkerListItem{
		{
			ID:   1,
			Name: "John Doe",
		},
		{
			ID:   2,
			Name: "Jane Smith",
		},
	}

	mockDomain := &mockUserDomain{
		workers: mockData,
		err:     nil,
	}

	uc := &UserUseCase{
		user: mockDomain,
	}

	req := GetWorkerListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "", // Empty search
		},
	}

	result, err := uc.GetWorkerList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}
}

func TestUserUseCase_GetWorkerList_CaseInsensitiveSearch(t *testing.T) {
	// Test case-insensitive search
	mockData := []userDmn.WorkerListItem{
		{
			ID:   1,
			Name: "John Doe",
		},
		{
			ID:   2,
			Name: "jane smith",
		},
	}

	mockDomain := &mockUserDomain{
		workers: mockData,
		err:     nil,
	}

	uc := &UserUseCase{
		user: mockDomain,
	}

	req := GetWorkerListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "JANE",
		},
	}

	result, err := uc.GetWorkerList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 1 {
		t.Errorf("Expected 1 result, got %d", len(result))
	}

	if result[0].Name != "jane smith" {
		t.Errorf("Expected name 'jane smith', got %s", result[0].Name)
	}
}

func TestUserUseCase_GetWorkerList_PartialNameMatch(t *testing.T) {
	// Test partial name matching
	mockData := []userDmn.WorkerListItem{
		{
			ID:   1,
			Name: "John Doe",
		},
		{
			ID:   2,
			Name: "Johnny Walker",
		},
		{
			ID:   3,
			Name: "Jane Smith",
		},
	}

	mockDomain := &mockUserDomain{
		workers: mockData,
		err:     nil,
	}

	uc := &UserUseCase{
		user: mockDomain,
	}

	req := GetWorkerListReq{
		BasicGetParam: types.BasicGetParam{
			Search: "john",
		},
	}

	result, err := uc.GetWorkerList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 2 {
		t.Errorf("Expected 2 results, got %d", len(result))
	}

	// Check that both John and Johnny are returned
	names := make(map[string]bool)
	for _, worker := range result {
		names[worker.Name] = true
	}

	if !names["John Doe"] {
		t.Error("Expected John Doe in results")
	}

	if !names["Johnny Walker"] {
		t.Error("Expected Johnny Walker in results")
	}
}

func TestUserUseCase_GetWorkerList_SingleWorker(t *testing.T) {
	// Test with single worker
	mockData := []userDmn.WorkerListItem{
		{
			ID:   1,
			Name: "Solo Worker",
		},
	}

	mockDomain := &mockUserDomain{
		workers: mockData,
		err:     nil,
	}

	uc := &UserUseCase{
		user: mockDomain,
	}

	req := GetWorkerListReq{
		BasicGetParam: types.BasicGetParam{},
	}

	result, err := uc.GetWorkerList(context.Background(), req)

	// Assertions
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if len(result) != 1 {
		t.Errorf("Expected 1 result, got %d", len(result))
	}

	if result[0].ID != 1 {
		t.Errorf("Expected ID 1, got %d", result[0].ID)
	}

	if result[0].Name != "Solo Worker" {
		t.Errorf("Expected name 'Solo Worker', got %s", result[0].Name)
	}
}
