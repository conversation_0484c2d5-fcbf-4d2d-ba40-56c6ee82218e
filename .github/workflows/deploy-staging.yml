name: Deploy to Staging Server

on:
  push:
    branches:
      - staging

jobs:
  deploy-staging:
    name: SSH Deploy (staging)
    runs-on: ubuntu-latest

    steps:
      - name: Execute deployment commands on staging server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.STAGING_SSH_HOST }}
          username: ${{ secrets.STAGING_SSH_USER }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            set -euo pipefail
            echo "📂 Changing directory to /var/www/be-nbs-accounting-system"
            cd /var/www/be-nbs-accounting-system

            echo "⬇️  Pulling latest code (staging)"
            eval "$(ssh-agent -s)"
            ssh-add ~/.ssh/id_ed25519_nbs_accounting_system
            git pull

            echo "📦 Tidying Go modules"
            go mod tidy

            echo "🔨 Building binary"
            cd cmd/accounting-system
            go build .

            echo "🚀 Restarting service (staging)"
            sudo service be-nbs-accounting-system restart

            echo "📋 Service status"
            sudo service be-nbs-accounting-system status
