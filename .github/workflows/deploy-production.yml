name: Deploy to Production Server

on:
  push:
    branches:
      - main

jobs:
  deploy-production:
    name: SSH Deploy (production)
    runs-on: ubuntu-latest

    steps:
      - name: Execute deployment commands on production server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PROD_SSH_HOST }}
          username: ${{ secrets.PROD_SSH_USER }}
          key: ${{ secrets.PROD_SSH_KEY }}
          script: |
            set -euo pipefail
            echo "📂 Changing directory to /var/www/be-nbs-accounting-system"
            cd /var/www/be-nbs-accounting-system

            echo "⬇️  Pulling latest code (production)"
            eval "$(ssh-agent -s)"
            ssh-add ~/.ssh/id_ed25519_nbs_accounting_system
            git pull

            echo "📦 Tidying Go modules"
            go mod tidy

            echo "🔨 Building binary"
            cd cmd/accounting-system
            go build .

            echo "🚀 Restarting service (production)"
            sudo service be-nbs-accounting-system restart

            echo "📋 Service status"
            sudo service be-nbs-accounting-system status
