package option

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// getList fetches all options ordered by title.
func (rsc OptionResource) getList(ctx context.Context, param GetListReq) ([]Option, error) {
	var options []Option

	db := dbmanager.Manager().WithContext(ctx)

	if param.Search != "" {
		db = db.Where("title ILIKE ?", "%"+param.Search+"%")
	}

	// Exclude soft deleted records and order by title ascending
	err := db.Where("deleted_at IS NULL").
		Order("title ASC").
		Find(&options).Error

	if err != nil {
		return []Option{}, log.LogError(err, nil)
	}

	return options, nil
}
