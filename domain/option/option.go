package option

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	OptionDomainItf interface {
		GetList(ctx context.Context, param GetListReq) ([]Option, error)
	}

	OptionResourceItf interface {
		getList(ctx context.Context, param GetListReq) ([]Option, error)
	}
)

// GetList retrieves all options ordered by title.
func (d *OptionDomain) GetList(ctx context.Context, param GetListReq) ([]Option, error) {
	options, err := d.resource.getList(ctx, param)
	if err != nil {
		return []Option{}, log.LogError(err, nil)
	}
	return options, nil
}
