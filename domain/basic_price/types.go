package basicprice

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
)

type BasicPrice struct {
	ID          int64      `gorm:"column:id;primary_key"`
	Code        string     `gorm:"column:code;unique"`
	Title       string     `gorm:"column:title"`
	Explanation string     `gorm:"column:explanation"`
	PriceJson   string     `gorm:"column:price_json"`
	CreatedAt   time.Time  `gorm:"column:created_at"`
	UpdatedAt   time.Time  `gorm:"column:updated_at"`
	DeletedAt   *time.Time `gorm:"column:deleted_at"`
}

type GetListReq struct {
	types.BasicGetParam
}

// GetByIDParam represents the parameters for getting basic price by ID
type GetByIDParam struct {
	ID int64
}
