package basicprice

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// getList fetches all basic prices ordered by title.
func (rsc BasicPriceResource) getList(ctx context.Context, param GetListReq) ([]BasicPrice, error) {
	var basicPrices []BasicPrice

	db := dbmanager.Manager().WithContext(ctx)

	if param.Search != "" {
		db = db.Where("title ILIKE ?", "%"+param.Search+"%")
	}

	// Exclude soft deleted records and order by title ascending
	err := db.Where("deleted_at IS NULL").
		Order("title ASC").
		Find(&basicPrices).Error

	if err != nil {
		return []BasicPrice{}, log.LogError(err, nil)
	}

	return basicPrices, nil
}

// getByID fetches basic price by ID.
func (rsc BasicPriceResource) getByID(ctx context.Context, param GetByIDParam) (BasicPrice, error) {
	var basicPrice BasicPrice

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("id = ?", param.ID).
		Where("deleted_at IS NULL").
		First(&basicPrice).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return BasicPrice{}, nil
		}
		return BasicPrice{}, log.LogError(err, nil)
	}

	return basicPrice, nil
}
