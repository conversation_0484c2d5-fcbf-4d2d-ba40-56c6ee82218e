package basicprice

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	BasicPriceDomainItf interface {
		GetList(ctx context.Context, param GetListReq) ([]BasicPrice, error)
		GetByID(ctx context.Context, param GetByIDParam) (BasicPrice, error)
	}

	BasicPriceResourceItf interface {
		getList(ctx context.Context, param GetListReq) ([]BasicPrice, error)
		getByID(ctx context.Context, param GetByIDParam) (BasicPrice, error)
	}
)

// GetList retrieves all basic prices ordered by title.
func (d *BasicPriceDomain) GetList(ctx context.Context, param GetListReq) ([]BasicPrice, error) {
	basicPrices, err := d.resource.getList(ctx, param)
	if err != nil {
		return []BasicPrice{}, log.LogError(err, nil)
	}
	return basicPrices, nil
}

// GetByID retrieves basic price by ID.
func (d *BasicPriceDomain) GetByID(ctx context.Context, param GetByIDParam) (BasicPrice, error) {
	basicPrice, err := d.resource.getByID(ctx, param)
	if err != nil {
		return BasicPrice{}, log.LogError(err, nil)
	}
	return basicPrice, nil
}
