package health

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	HealthDomainItf interface {
		GetHealth(ctx context.Context) (GetHealth, error)
	}

	HealthResourceItf interface {
		// database
		getHealth(ctx context.Context) (GetHealth, error)
	}
)

func (c *HealthDomain) GetHealth(ctx context.Context) (GetHealth, error) {
	health, err := c.resource.getHealth(ctx)
	if err != nil {
		return GetHealth{}, log.LogError(err, nil)
	}

	return health, nil
}
