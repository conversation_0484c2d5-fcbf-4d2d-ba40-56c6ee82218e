package userrole

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/domain/role"
	"github.com/Sera-Global/be-nbs-accounting-system/domain/user"
)

type UserRole struct {
	ID        int64      `gorm:"column:id;primary_key"`
	UserID    int64      `gorm:"column:user_id"`
	RoleID    int64      `gorm:"column:role_id"`
	CreatedAt time.Time  `gorm:"column:created_at"`
	UpdatedAt time.Time  `gorm:"column:updated_at"`
	DeletedAt *time.Time `gorm:"column:deleted_at"`

	User user.User `gorm:"foreignkey:UserID"`
	Role role.Role `gorm:"foreignkey:RoleID"`
}
