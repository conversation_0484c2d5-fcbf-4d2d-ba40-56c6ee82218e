package userrole

import "context"

type (
	UserRoleDomainItf interface {
		GetRolesByUserID(ctx context.Context, userID int64) ([]string, error)
	}

	UserRoleResourceItf interface {
		getRolesByUserID(ctx context.Context, userID int64) ([]string, error)
	}
)

// GetRolesByUserID retrieves role names for a specific userID.
func (d *UserRoleDomain) GetRolesByUserID(ctx context.Context, userID int64) ([]string, error) {
	userRole, err := d.resource.getRolesByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	return userRole, nil
}
