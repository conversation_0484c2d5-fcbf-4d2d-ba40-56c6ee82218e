package userrole

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// getRolesByUserID fetches role names for a specific userID (resource layer).
func (rsc UserRoleResource) getRolesByUserID(ctx context.Context, userID int64) ([]string, error) {
	var roles []string
	db := dbmanager.Manager().WithContext(ctx)
	if err := db.Table("user_role").
		Select("role.name").
		Joins("JOIN role ON role.id = user_role.role_id").
		Where("user_role.user_id = ?", userID).
		Pluck("role.name", &roles).Error; err != nil {
		return nil, err
	}
	return roles, nil
}
