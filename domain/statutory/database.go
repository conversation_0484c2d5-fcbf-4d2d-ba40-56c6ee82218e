package statutory

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// getList fetches all statutory records ordered by title.
func (rsc StatutoryResource) getList(ctx context.Context, param GetListReq) ([]Statutory, error) {
	var statutories []Statutory

	db := dbmanager.Manager().WithContext(ctx)

	if param.Search != "" {
		db = db.Where("title ILIKE ?", "%"+param.Search+"%")
	}

	// Exclude soft deleted records and order by title ascending
	err := db.Where("deleted_at IS NULL").
		Order("title ASC").
		Find(&statutories).Error

	if err != nil {
		return []Statutory{}, log.LogError(err, nil)
	}

	return statutories, nil
}

// getByCustomerID fetches statutory record by customer ID through customer table join.
func (rsc StatutoryResource) getByCustomerID(ctx context.Context, param GetByCustomerIDParam) (Statutory, error) {
	var statutory Statutory

	db := dbmanager.Manager().WithContext(ctx)

	// Join customer table with statutory table using customer.statutory_id
	err := db.Table("statutory").
		Select("statutory.*").
		Joins("INNER JOIN customer ON customer.statutory_id = statutory.id").
		Where("customer.id = ?", param.CustomerID).
		Where("customer.deleted_at IS NULL").
		Where("statutory.deleted_at IS NULL").
		First(&statutory).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return Statutory{}, nil
		}
		return Statutory{}, log.LogError(err, nil)
	}

	return statutory, nil
}
