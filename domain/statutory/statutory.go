package statutory

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	StatutoryDomainItf interface {
		GetList(ctx context.Context, param GetListReq) ([]Statutory, error)
		GetByCustomerID(ctx context.Context, param GetByCustomerIDParam) (Statutory, error)
	}

	StatutoryResourceItf interface {
		getList(ctx context.Context, param GetListReq) ([]Statutory, error)
		getByCustomerID(ctx context.Context, param GetByCustomerIDParam) (Statutory, error)
	}
)

// GetList retrieves statutory records.
func (d *StatutoryDomain) GetList(ctx context.Context, param GetListReq) ([]Statutory, error) {
	statutories, err := d.resource.getList(ctx, param)
	if err != nil {
		return []Statutory{}, log.LogError(err, nil)
	}
	return statutories, nil
}

// GetByCustomerID retrieves statutory record by customer ID through customer table join.
func (d *StatutoryDomain) GetByCustomerID(ctx context.Context, param GetByCustomerIDParam) (Statutory, error) {
	statutory, err := d.resource.getByCustomerID(ctx, param)
	if err != nil {
		return Statutory{}, log.LogError(err, nil)
	}
	return statutory, nil
}
