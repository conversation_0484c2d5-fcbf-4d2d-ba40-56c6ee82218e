package block

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	BlockDomainItf interface {
		GetListWithDistricts(ctx context.Context, param GetListReq) ([]BlockWithDistricts, error)
	}

	BlockResourceItf interface {
		getListWithDistricts(ctx context.Context, param GetListReq) ([]BlockWithDistricts, error)
	}
)

// GetListWithDistricts retrieves all blocks with their associated districts ordered by block name.
func (d *BlockDomain) GetListWithDistricts(ctx context.Context, param GetListReq) ([]BlockWithDistricts, error) {
	blocks, err := d.resource.getListWithDistricts(ctx, param)
	if err != nil {
		return []BlockWithDistricts{}, log.LogError(err, nil)
	}
	return blocks, nil
}
