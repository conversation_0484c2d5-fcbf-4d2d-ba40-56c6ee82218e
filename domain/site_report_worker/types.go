package sitereportworker

import "time"

type SiteReportWorker struct {
	ID                    int64      `gorm:"column:id;primary_key"`
	SiteReportID          int64      `gorm:"column:site_report_id"`
	UserID                int64      `gorm:"column:user_id"`
	StartTime             time.Time  `gorm:"column:start_time;type:time"`
	EndTime               time.Time  `gorm:"column:end_time;type:time"`
	BreakTime             *time.Time `gorm:"column:break_time;type:time"`
	TransportationExpense float64    `gorm:"column:transportation_expense"`
	LeaderAllowance       float64    `gorm:"column:leader_allowance"`
	DistantFeeID          *int64     `gorm:"column:distant_fee_id"`
	IncomeTaxID           int64      `gorm:"column:income_tax_id"`
	Tax                   float64    `gorm:"column:tax"`
	Amount                float64    `gorm:"column:amount"`
	Snapshot              string     `gorm:"column:snapshot;type:json"`
	Status                string     `gorm:"column:status"`
	IssuedDate            *time.Time `gorm:"column:issued_date;type:date"`
}

type Snapshot struct {
	WorkerName string  `json:"worker_name"`
	DistantFee float64 `json:"distant_fee"`
}

// UpdateWorkerParam represents the parameters for updating site report worker
type UpdateWorkerParam struct {
	ID                    int64
	UserID                int64
	StartTime             time.Time
	EndTime               time.Time
	BreakTime             *time.Time
	TransportationExpense float64
	LeaderAllowance       float64
	DistantFeeID          *int64
	IncomeTaxID           int64
	Tax                   float64
	Amount                float64
	Snapshot              string
}

// DeleteWorkerParam represents the parameters for deleting site report worker
type DeleteWorkerParam struct {
	ID int64
}

// GetWorkerByIDParam represents the parameters for getting site report worker by ID
type GetWorkerByIDParam struct {
	ID int64
}
