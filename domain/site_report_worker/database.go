package sitereportworker

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// updateWorker updates an existing site report worker record.
func (rsc SiteReportWorkerResource) updateWorkerWithTx(ctx context.Context, tx *gorm.DB, param UpdateWorkerParam) error {
	db := tx.WithContext(ctx)

	updateFields := map[string]interface{}{
		"user_id":                param.UserID,
		"start_time":             param.StartTime,
		"end_time":               param.EndTime,
		"break_time":             param.BreakTime,
		"transportation_expense": param.TransportationExpense,
		"leader_allowance":       param.LeaderAllowance,
		"distant_fee_id":         param.DistantFeeID,
		"income_tax_id":          param.IncomeTaxID,
		"tax":                    param.Tax,
		"amount":                 param.Amount,
		"snapshot":               param.Snapshot,
	}

	err := db.Model(&SiteReportWorker{}).
		Where("id = ?", param.ID).
		Updates(updateFields).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// deleteWorker deletes a site report worker record.
func (rsc SiteReportWorkerResource) deleteWorkerWithTx(ctx context.Context, tx *gorm.DB, param DeleteWorkerParam) error {
	db := tx.WithContext(ctx)

	err := db.Delete(&SiteReportWorker{}, param.ID).Error
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// getWorkerByID fetches a site report worker by ID.
func (rsc SiteReportWorkerResource) getWorkerByID(ctx context.Context, param GetWorkerByIDParam) (SiteReportWorker, error) {
	var worker SiteReportWorker

	db := dbmanager.Manager().WithContext(ctx)
	err := db.Where("id = ?", param.ID).First(&worker).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return SiteReportWorker{}, nil
		}
		return SiteReportWorker{}, log.LogError(err, nil)
	}

	return worker, nil
}
