package user

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	UserDomainItf interface {
		GetByUsername(ctx context.Context, username string) (User, error)
		GetLoginUser(ctx context.Context, param User) error
		GetWorkerList(ctx context.Context, param GetWorkerListReq) ([]WorkerListItem, error)
	}

	UserResourceItf interface {
		getByUsername(ctx context.Context, username string) (User, error)
		getLoginUser(ctx context.Context, param User) error
		getWorkerList(ctx context.Context, param GetWorkerListReq) ([]WorkerListItem, error)
	}
)

// GetByUsername retrieves user by username.
func (d *UserDomain) GetByUsername(ctx context.Context, username string) (User, error) {
	user, err := d.resource.getByUsername(ctx, username)
	if err != nil {
		return User{}, log.LogError(err, nil)
	}
	return user, nil
}

// GetLoginUser retrieves user by username and password.
func (d *UserDomain) GetLoginUser(ctx context.Context, param User) error {
	err := d.resource.getLoginUser(ctx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

// GetWorkerList retrieves users with worker role.
func (d *UserDomain) GetWorkerList(ctx context.Context, param GetWorkerListReq) ([]WorkerListItem, error) {
	workers, err := d.resource.getWorkerList(ctx, param)
	if err != nil {
		return []WorkerListItem{}, log.LogError(err, nil)
	}
	return workers, nil
}
