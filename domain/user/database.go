package user

import (
	"context"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// getByUsername fetches a user by username.
func (rsc UserResource) getByUsername(ctx context.Context, username string) (User, error) {
	var user User

	db := dbmanager.Manager().WithContext(ctx)
	err := db.Where("username = ?", username).
		Where("deleted_at IS NULL").
		First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return User{}, nil
		}
		return User{}, log.LogError(err, nil)
	}

	return user, nil
}

// getByID fetches a user by ID.
func (rsc UserResource) getByID(ctx context.Context, id int64) (User, error) {
	var user User

	db := dbmanager.Manager().WithContext(ctx)
	err := db.Where("id = ?", id).
		First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return User{}, nil
		}
		return User{}, log.LogError(err, nil)
	}

	return user, nil
}

// getLoginUser fetches a user by username and password.
func (rsc UserResource) getLoginUser(ctx context.Context, param User) error {
	db := dbmanager.Manager().WithContext(ctx)
	user := User{}

	if param.Username != "" {
		db = db.Where("username = ?", param.Username)
	}

	err := db.First(&user).Error
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// getWorkerList fetches all users with worker role ordered by name.
func (rsc UserResource) getWorkerList(ctx context.Context, param GetWorkerListReq) ([]WorkerListItem, error) {
	db := dbmanager.Manager().WithContext(ctx)

	// Build the query to get users with worker role
	query := `
		SELECT
			u.id,
			u.name
		FROM "user" u
		INNER JOIN user_role ur ON u.id = ur.user_id
		INNER JOIN role r ON ur.role_id = r.id
		WHERE u.deleted_at IS NULL
		AND r.name = ?`

	// Add search filter if provided
	args := []interface{}{constanta.RoleWorker}
	if param.Search != "" {
		query += " AND u.name ILIKE ?"
		args = append(args, "%"+param.Search+"%")
	}

	query += " ORDER BY u.name ASC"

	// Execute the query
	rows, err := db.Raw(query, args...).Rows()
	if err != nil {
		return []WorkerListItem{}, log.LogError(err, nil)
	}
	defer rows.Close()

	var workers []WorkerListItem
	for rows.Next() {
		var worker WorkerListItem
		err := rows.Scan(&worker.ID, &worker.Name)
		if err != nil {
			return []WorkerListItem{}, log.LogError(err, nil)
		}
		workers = append(workers, worker)
	}

	if err = rows.Err(); err != nil {
		return []WorkerListItem{}, log.LogError(err, nil)
	}

	return workers, nil
}
