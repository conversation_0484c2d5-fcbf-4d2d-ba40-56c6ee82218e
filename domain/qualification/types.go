package qualification

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
)

type Qualification struct {
	ID          int64      `gorm:"column:id;primary_key"`
	Code        string     `gorm:"column:code;unique"`
	Title       string     `gorm:"column:title"`
	Explanation string     `gorm:"column:explanation"`
	AddClaim    float64    `gorm:"column:add_claim"`
	PaidAmount  float64    `gorm:"column:paid_amount"`
	CreatedAt   time.Time  `gorm:"column:created_at"`
	UpdatedAt   time.Time  `gorm:"column:updated_at"`
	DeletedAt   *time.Time `gorm:"column:deleted_at"`
}

type GetListReq struct {
	types.BasicGetParam
}

// GetByIDParam represents the parameters for getting qualification by ID
type GetByIDParam struct {
	ID int64
}

// GetByIDsParam represents the parameters for getting qualifications by IDs
type GetByIDsParam struct {
	IDs []int64
}
