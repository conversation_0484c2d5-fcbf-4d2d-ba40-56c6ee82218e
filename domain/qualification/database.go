package qualification

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// getList fetches all qualifications ordered by title.
func (rsc QualificationResource) getList(ctx context.Context, param GetListReq) ([]Qualification, error) {
	var qualifications []Qualification

	db := dbmanager.Manager().WithContext(ctx)

	if param.Search != "" {
		db = db.Where("title ILIKE ?", "%"+param.Search+"%")
	}

	// Exclude soft deleted records and order by title ascending
	err := db.Where("deleted_at IS NULL").
		Order("title ASC").
		Find(&qualifications).Error

	if err != nil {
		return []Qualification{}, log.LogError(err, nil)
	}

	return qualifications, nil
}

// getByIDs fetches qualifications by IDs.
func (rsc QualificationResource) getByIDs(ctx context.Context, param GetByIDsParam) ([]Qualification, error) {
	var qualifications []Qualification

	if len(param.IDs) == 0 {
		return qualifications, nil
	}

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("id IN ?", param.IDs).
		Order("title ASC").
		Find(&qualifications).Error

	if err != nil {
		return []Qualification{}, log.LogError(err, nil)
	}

	return qualifications, nil
}
