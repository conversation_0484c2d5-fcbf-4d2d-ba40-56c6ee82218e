package dailyreportaddition

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	DailyReportAdditionDomainItf interface {
		GetList(ctx context.Context, param GetListReq) ([]DailyReportAddition, error)
		GetByID(ctx context.Context, param GetByIDParam) (DailyReportAddition, error)
	}

	DailyReportAdditionResourceItf interface {
		getList(ctx context.Context, param GetListReq) ([]DailyReportAddition, error)
		getByID(ctx context.Context, param GetByIDParam) (DailyReportAddition, error)
	}
)

// GetList retrieves daily report addition records.
func (d *DailyReportAdditionDomain) GetList(ctx context.Context, param GetListReq) ([]DailyReportAddition, error) {
	additions, err := d.resource.getList(ctx, param)
	if err != nil {
		return []DailyReportAddition{}, log.LogError(err, nil)
	}
	return additions, nil
}

// GetByID retrieves daily report addition record by ID.
func (d *DailyReportAdditionDomain) GetByID(ctx context.Context, param GetByIDParam) (DailyReportAddition, error) {
	addition, err := d.resource.getByID(ctx, param)
	if err != nil {
		return DailyReportAddition{}, log.LogError(err, nil)
	}
	return addition, nil
}
