package dailyreportaddition

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// getList fetches all daily report addition records ordered by title.
func (rsc DailyReportAdditionResource) getList(ctx context.Context, param GetListReq) ([]DailyReportAddition, error) {
	var additions []DailyReportAddition

	db := dbmanager.Manager().WithContext(ctx)

	if param.Search != "" {
		db = db.Where("title ILIKE ?", "%"+param.Search+"%")
	}

	// Exclude soft deleted records and order by title ascending
	err := db.Where("deleted_at IS NULL").
		Order("title ASC").
		Find(&additions).Error

	if err != nil {
		return []DailyReportAddition{}, log.LogError(err, nil)
	}

	return additions, nil
}

// getByID fetches daily report addition record by ID.
func (rsc DailyReportAdditionResource) getByID(ctx context.Context, param GetByIDParam) (DailyReportAddition, error) {
	var addition DailyReportAddition

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("id = ?", param.ID).
		Where("deleted_at IS NULL").
		First(&addition).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return DailyReportAddition{}, nil
		}
		return DailyReportAddition{}, log.LogError(err, nil)
	}

	return addition, nil
}
