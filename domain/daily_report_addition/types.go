package dailyreportaddition

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
)

type DailyReportAddition struct {
	ID              int64      `gorm:"column:id;primary_key"`
	Code            string     `gorm:"column:code;unique"`
	Title           string     `gorm:"column:title"`
	Explanation     string     `gorm:"column:explanation"`
	AmountPerSite   float64    `gorm:"column:amount_per_site"`
	AmountPerHour   float64    `gorm:"column:amount_per_hour"`
	WelfareSubject  string     `gorm:"column:welfare_subject"`
	CreatedAt       time.Time  `gorm:"column:created_at"`
	UpdatedAt       time.Time  `gorm:"column:updated_at"`
	DeletedAt       *time.Time `gorm:"column:deleted_at"`
}

type GetListReq struct {
	types.BasicGetParam
}

// GetByIDParam represents the parameters for getting daily report addition by ID
type GetByIDParam struct {
	ID int64
}
