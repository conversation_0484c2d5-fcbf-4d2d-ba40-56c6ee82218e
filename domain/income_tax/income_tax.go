package incometax

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	IncomeTaxDomainItf interface {
		GetByAmount(ctx context.Context, param GetByAmountParam) (IncomeTax, error)
	}

	IncomeTaxResourceItf interface {
		getByAmount(ctx context.Context, param GetByAmountParam) (IncomeTax, error)
	}
)

// GetByAmount retrieves income tax record by amount range.
func (d *IncomeTaxDomain) GetByAmount(ctx context.Context, param GetByAmountParam) (IncomeTax, error) {
	incomeTax, err := d.resource.getByAmount(ctx, param)
	if err != nil {
		return IncomeTax{}, log.LogError(err, nil)
	}
	return incomeTax, nil
}
