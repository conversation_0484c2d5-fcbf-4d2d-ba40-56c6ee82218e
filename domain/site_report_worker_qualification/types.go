package sitereportworkerqualification

type SiteReportWorkerQualification struct {
	ID                 int64  `gorm:"column:id;primary_key"`
	SiteReportWorkerID int64  `gorm:"column:site_report_worker_id"`
	QualificationID    int64  `gorm:"column:qualification_id"`
	Snapshot           string `gorm:"column:snapshot;type:json"`
}

type Snapshot struct {
	Title    string  `json:"title"`
	AddClaim float64 `json:"add_claim"`
}

// BulkInsertParam represents the parameters for bulk inserting site report worker qualifications
type BulkInsertParam struct {
	SiteReportWorkerID int64
	Qualifications     []QualificationData
}

type QualificationData struct {
	QualificationID int64
	Snapshot        string
}

// BulkUpdateParam represents the parameters for bulk updating site report worker qualifications
type BulkUpdateParam struct {
	SiteReportWorkerID int64
	Qualifications     []QualificationUpdateData
}

type QualificationUpdateData struct {
	QualificationID int64
	Snapshot        string
}

// BulkDeleteParam represents the parameters for bulk deleting site report worker qualifications
type BulkDeleteParam struct {
	SiteReportWorkerID int64
	QualificationIDs   []int64
}

// GetByWorkerIDParam represents the parameters for getting qualifications by worker ID
type GetByWorkerIDParam struct {
	SiteReportWorkerID int64
}
