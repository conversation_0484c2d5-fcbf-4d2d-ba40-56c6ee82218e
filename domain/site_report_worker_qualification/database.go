package sitereportworkerqualification

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// bulkInsert creates multiple site report worker qualification records.
func (rsc SiteReportWorkerQualificationResource) bulkInsertWithTx(ctx context.Context, tx *gorm.DB, param BulkInsertParam) error {
	if len(param.Qualifications) == 0 {
		return nil
	}

	db := tx.WithContext(ctx)

	var qualifications []SiteReportWorkerQualification
	for _, qual := range param.Qualifications {
		qualifications = append(qualifications, SiteReportWorkerQualification{
			SiteReportWorkerID: param.SiteReportWorkerID,
			QualificationID:    qual.QualificationID,
			Snapshot:           qual.Snapshot,
		})
	}

	err := db.Create(&qualifications).Error
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// bulkUpdate updates multiple site report worker qualification records.
func (rsc SiteReportWorkerQualificationResource) bulkUpdateWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateParam) error {
	if len(param.Qualifications) == 0 {
		return nil
	}

	db := tx.WithContext(ctx)

	// Update each qualification individually
	for _, qual := range param.Qualifications {
		err := db.Model(&SiteReportWorkerQualification{}).
			Where("site_report_worker_id = ? AND qualification_id = ?", param.SiteReportWorkerID, qual.QualificationID).
			Update("snapshot", qual.Snapshot).Error
		if err != nil {
			return log.LogError(err, nil)
		}
	}

	return nil
}

// bulkDelete deletes multiple site report worker qualification records.
func (rsc SiteReportWorkerQualificationResource) bulkDeleteWithTx(ctx context.Context, tx *gorm.DB, param BulkDeleteParam) error {
	db := tx.WithContext(ctx)

	if param.SiteReportWorkerID > 0 {
		db = db.Where("site_report_worker_id = ?", param.SiteReportWorkerID)
	}

	if len(param.QualificationIDs) > 0 {
		db = db.Where("qualification_id IN ?", param.QualificationIDs)
	}

	err := db.Delete(&SiteReportWorkerQualification{}).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// getByWorkerID fetches site report worker qualifications by worker ID.
func (rsc SiteReportWorkerQualificationResource) getByWorkerID(ctx context.Context, param GetByWorkerIDParam) ([]SiteReportWorkerQualification, error) {
	var qualifications []SiteReportWorkerQualification

	db := dbmanager.Manager().WithContext(ctx)
	err := db.Where("site_report_worker_id = ?", param.SiteReportWorkerID).
		Find(&qualifications).Error

	if err != nil {
		return []SiteReportWorkerQualification{}, log.LogError(err, nil)
	}

	return qualifications, nil
}
