package departmentpic

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	DepartmentPicDomainItf interface {
		GetListByDepartmentID(ctx context.Context, param GetListByDepartmentIDParam) ([]DepartmentPic, error)
	}

	DepartmentPicResourceItf interface {
		getListByDepartmentID(ctx context.Context, param GetListByDepartmentIDParam) ([]DepartmentPic, error)
	}
)

// GetListByDepartmentID retrieves all department PICs for a specific department ordered by pic_name.
func (d *DepartmentPicDomain) GetListByDepartmentID(ctx context.Context, param GetListByDepartmentIDParam) ([]DepartmentPic, error) {
	departmentPics, err := d.resource.getListByDepartmentID(ctx, param)
	if err != nil {
		return []DepartmentPic{}, log.LogError(err, nil)
	}
	return departmentPics, nil
}
