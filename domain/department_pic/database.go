package departmentpic

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// getListByDepartmentID fetches all department PICs for a specific department ordered by pic_name.
func (rsc DepartmentPicResource) getListByDepartmentID(ctx context.Context, param GetListByDepartmentIDParam) ([]DepartmentPic, error) {
	var departmentPics []DepartmentPic

	db := dbmanager.Manager().WithContext(ctx)

	if param.Search != "" {
		db = db.Where("pic_name ILIKE ?", "%"+param.Search+"%")
	}

	// Exclude soft deleted records, filter by department_id, and order by pic_name ascending
	err := db.Where("deleted_at IS NULL AND department_id = ?", param.DepartmentID).
		Order("pic_name ASC").
		Find(&departmentPics).Error

	if err != nil {
		return []DepartmentPic{}, log.LogError(err, nil)
	}

	return departmentPics, nil
}
