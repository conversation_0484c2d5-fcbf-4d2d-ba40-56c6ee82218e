package distantfee

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
)

type Distant<PERSON>ee struct {
	ID               int64      `gorm:"column:id;primary_key"`
	Code             string     `gorm:"column:code;unique"`
	Title            string     `gorm:"column:title"`
	Explanation      string     `gorm:"column:explanation"`
	Unit             float64    `gorm:"column:unit"`
	AddAmountPerHour float64    `gorm:"column:add_amount_per_hour"`
	CreatedAt        time.Time  `gorm:"column:created_at"`
	UpdatedAt        time.Time  `gorm:"column:updated_at"`
	DeletedAt        *time.Time `gorm:"column:deleted_at"`
}

type GetListParam struct {
	types.BasicGetParam
}

// GetByIDParam represents the parameters for getting distant fee by ID
type GetByIDParam struct {
	ID int64
}
