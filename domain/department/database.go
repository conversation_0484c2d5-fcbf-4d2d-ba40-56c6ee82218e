package department

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// getListByCustomerID fetches all departments for a specific customer ordered by name.
func (rsc DepartmentResource) getListByCustomerID(ctx context.Context, param GetListByCustomerIDParam) ([]Department, error) {
	var departments []Department

	db := dbmanager.Manager().WithContext(ctx)

	if param.Search != "" {
		db = db.Where("name ILIKE ?", "%"+param.Search+"%")
	}

	// Exclude soft deleted records, filter by customer_id, and order by name ascending
	err := db.Where("deleted_at IS NULL AND customer_id = ?", param.CustomerID).
		Order("name ASC").
		Find(&departments).Error

	if err != nil {
		return []Department{}, log.LogError(err, nil)
	}

	return departments, nil
}
