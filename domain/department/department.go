package department

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	DepartmentDomainItf interface {
		GetListByCustomerID(ctx context.Context, param GetListByCustomerIDParam) ([]Department, error)
	}

	DepartmentResourceItf interface {
		getListByCustomerID(ctx context.Context, param GetListByCustomerIDParam) ([]Department, error)
	}
)

// GetListByCustomerID retrieves all departments for a specific customer ordered by name.
func (d *DepartmentDomain) GetListByCustomerID(ctx context.Context, param GetListByCustomerIDParam) ([]Department, error) {
	departments, err := d.resource.getListByCustomerID(ctx, param)
	if err != nil {
		return []Department{}, log.LogError(err, nil)
	}
	return departments, nil
}
