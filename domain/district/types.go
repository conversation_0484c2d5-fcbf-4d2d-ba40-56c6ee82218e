package district

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/domain/block"
)

type District struct {
	ID        int64      `gorm:"column:id;primary_key"`
	BlockID   int64      `gorm:"column:block_id"`
	Code      string     `gorm:"column:code;unique"`
	Name      string     `gorm:"column:name"`
	CreatedAt time.Time  `gorm:"column:created_at"`
	UpdatedAt time.Time  `gorm:"column:updated_at"`
	DeletedAt *time.Time `gorm:"column:deleted_at"`

	Block block.Block `gorm:"foreignkey:BlockID"`
}
