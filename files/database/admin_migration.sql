-- PostgreSQL-compatible migration script
-- Sequence columns use the SERIAL shorthand so explicit id values
--   from the INSERT statements continue to work.
-- All timestamp columns use timestamptz for timezone safety.
-- ─────────────────────────────────────────────────────────────

BEGIN;

--──────────────────────────────────────────────────────────────
-- Drop tables (order matters to avoid FK issues)
--──────────────────────────────────────────────────────────────
DROP TABLE IF EXISTS goadmin_role_menu          CASCADE;
DROP TABLE IF EXISTS goadmin_role_permissions   CASCADE;
DROP TABLE IF EXISTS goadmin_role_users         CASCADE;
DROP TABLE IF EXISTS goadmin_user_permissions   CASCADE;
DROP TABLE IF EXISTS goadmin_operation_log      CASCADE;
DROP TABLE IF EXISTS goadmin_session            CASCADE;
DROP TABLE IF EXISTS goadmin_menu               CASCADE;
DROP TABLE IF EXISTS goadmin_permissions        CASCADE;
DROP TABLE IF EXISTS goadmin_roles              CASCADE;
DROP TABLE IF EXISTS goadmin_users              CASCADE;
DROP TABLE IF EXISTS goadmin_site               CASCADE;

--──────────────────────────────────────────────────────────────
-- Schema definitions
--──────────────────────────────────────────────────────────────

CREATE TABLE goadmin_menu (
    id           SERIAL PRIMARY KEY,
    parent_id    integer    NOT NULL DEFAULT 0,
    type         smallint   NOT NULL DEFAULT 0,
    "order"      integer    NOT NULL DEFAULT 0,
    title        varchar(50)  NOT NULL,
    icon         varchar(50)  NOT NULL,
    uri          varchar(3000) NOT NULL DEFAULT '',
    header       varchar(150),
    plugin_name  varchar(150) NOT NULL DEFAULT '',
    uuid         varchar(150),
    created_at   timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at   timestamptz DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE goadmin_operation_log (
    id         SERIAL PRIMARY KEY,
    user_id    integer      NOT NULL,
    path       varchar(255) NOT NULL,
    method     varchar(10)  NOT NULL,
    ip         varchar(15)  NOT NULL,
    input      text         NOT NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX goadmin_operation_log_user_id_idx ON goadmin_operation_log(user_id);

CREATE TABLE goadmin_site (
    id          SERIAL PRIMARY KEY,
    key         varchar(100),
    value       text,
    description varchar(3000),
    state       smallint     NOT NULL DEFAULT 0,
    created_at  timestamptz  DEFAULT CURRENT_TIMESTAMP,
    updated_at  timestamptz  DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE goadmin_permissions (
    id          SERIAL PRIMARY KEY,
    name        varchar(50)  NOT NULL,
    slug        varchar(50)  NOT NULL,
    http_method varchar(255),
    http_path   text         NOT NULL,
    created_at  timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at  timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT admin_permissions_name_unique UNIQUE (name)
);

CREATE TABLE goadmin_role_menu (
    role_id     integer NOT NULL,
    menu_id     integer NOT NULL,
    created_at  timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at  timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT admin_role_menu_role_id_menu_id_unique UNIQUE (role_id, menu_id)
);

CREATE TABLE goadmin_role_permissions (
    role_id     integer NOT NULL,
    permission_id integer NOT NULL,
    created_at  timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at  timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT admin_role_permissions_unique UNIQUE (role_id, permission_id)
);

CREATE TABLE goadmin_role_users (
    role_id    integer NOT NULL,
    user_id    integer NOT NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT admin_user_roles_unique UNIQUE (role_id, user_id)
);

CREATE TABLE goadmin_roles (
    id          SERIAL PRIMARY KEY,
    name        varchar(50)  NOT NULL,
    slug        varchar(50)  NOT NULL,
    created_at  timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at  timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT admin_roles_name_unique UNIQUE (name)
);

CREATE TABLE goadmin_session (
    id          SERIAL PRIMARY KEY,
    sid         varchar(50)  NOT NULL DEFAULT '',
    values      varchar(3000) NOT NULL DEFAULT '',
    created_at  timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at  timestamptz DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE goadmin_user_permissions (
    user_id     integer NOT NULL,
    permission_id integer NOT NULL,
    created_at  timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at  timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT admin_user_permissions UNIQUE (user_id, permission_id)
);

CREATE TABLE goadmin_users (
    id             SERIAL PRIMARY KEY,
    username       varchar(100) NOT NULL,
    password       varchar(100) NOT NULL DEFAULT '',
    name           varchar(100) NOT NULL,
    avatar         varchar(255),
    remember_token varchar(100),
    created_at     timestamptz DEFAULT CURRENT_TIMESTAMP,
    updated_at     timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT admin_users_username_unique UNIQUE (username)
);

-- Example table (already PostgreSQL compatible in original dump)
CREATE TABLE IF NOT EXISTS public.example (
    id              SERIAL PRIMARY KEY,
    name            varchar(50),
    gender          smallint,
    city            varchar(50),
    ip              varchar(20),
    phone           varchar(10),
    created_at      timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at      timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    pricing_json    json,
    CONSTRAINT example_name_unique UNIQUE (name)
);

-- Seed data

COMMIT;
