package qualification

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	qualificationusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/qualification"
	"github.com/labstack/echo/v4"
)

func TestGetListRequestBinding(t *testing.T) {
	e := echo.New()

	tests := []struct {
		name       string
		search     string
		expectBind bool
	}{
		{
			name:       "Valid search parameter",
			search:     "Qualification",
			expectBind: true,
		},
		{
			name:       "Empty search parameter",
			search:     "",
			expectBind: true,
		},
		{
			name:       "Search with special characters",
			search:     "Qualification-1",
			expectBind: true,
		},
		{
			name:       "No search parameter",
			search:     "",
			expectBind: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request with query parameters
			q := make(url.Values)
			if tt.search != "" {
				q.Set("search", tt.search)
			}

			req := httptest.NewRequest(http.MethodGet, "/?"+q.Encode(), nil)
			rec := httptest.NewRecorder()
			c := e.New<PERSON>ontext(req, rec)

			// Test request binding
			var reqData qualificationusecase.GetListReq
			err := c.Bind(&reqData)

			if tt.expectBind && err != nil {
				t.Errorf("Expected successful binding, got error: %v", err)
			}

			if tt.expectBind && reqData.Search != tt.search {
				t.Errorf("Expected search '%s', got '%s'", tt.search, reqData.Search)
			}
		})
	}
}

func TestGetListResponseFormat(t *testing.T) {
	// Test that the response format matches the expected JSON structure
	expectedResponse := map[string]interface{}{
		"message": "Success",
		"data": []map[string]interface{}{
			{
				"id":   float64(1),
				"name": "qualification 1",
			},
			{
				"id":   float64(2),
				"name": "qualification 2",
			},
		},
	}

	// Verify the structure can be marshaled/unmarshaled correctly
	jsonData, err := json.Marshal(expectedResponse)
	if err != nil {
		t.Errorf("Failed to marshal expected response: %v", err)
	}

	var unmarshaled map[string]interface{}
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Errorf("Failed to unmarshal response: %v", err)
	}

	// Check structure
	if unmarshaled["message"] != "Success" {
		t.Errorf("Expected message 'Success', got %v", unmarshaled["message"])
	}

	data, ok := unmarshaled["data"].([]interface{})
	if !ok {
		t.Error("Expected data to be an array")
	}

	if len(data) != 2 {
		t.Errorf("Expected 2 data items, got %d", len(data))
	}

	// Check first item structure
	firstItem, ok := data[0].(map[string]interface{})
	if !ok {
		t.Error("Expected first item to be an object")
	}

	if firstItem["id"] != float64(1) {
		t.Errorf("Expected first item id to be 1, got %v", firstItem["id"])
	}

	if firstItem["name"] != "qualification 1" {
		t.Errorf("Expected first item name to be 'qualification 1', got %v", firstItem["name"])
	}
}

func TestGetListQueryParameterParsing(t *testing.T) {
	e := echo.New()

	tests := []struct {
		name           string
		queryString    string
		expectedSearch string
	}{
		{
			name:           "Simple search",
			queryString:    "search=qua",
			expectedSearch: "qua",
		},
		{
			name:           "URL encoded search",
			queryString:    "search=qualification%201",
			expectedSearch: "qualification 1",
		},
		{
			name:           "Empty search",
			queryString:    "search=",
			expectedSearch: "",
		},
		{
			name:           "No search parameter",
			queryString:    "",
			expectedSearch: "",
		},
		{
			name:           "Search with special characters",
			queryString:    "search=test%2Bqualification",
			expectedSearch: "test+qualification",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(http.MethodGet, "/?"+tt.queryString, nil)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			var reqData qualificationusecase.GetListReq
			err := c.Bind(&reqData)

			if err != nil {
				t.Errorf("Unexpected binding error: %v", err)
			}

			if reqData.Search != tt.expectedSearch {
				t.Errorf("Expected search '%s', got '%s'", tt.expectedSearch, reqData.Search)
			}
		})
	}
}
