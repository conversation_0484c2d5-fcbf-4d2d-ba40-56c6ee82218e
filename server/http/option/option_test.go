package option

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	optionusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/option"
	"github.com/labstack/echo/v4"
)

func TestGetListRequestBinding(t *testing.T) {
	e := echo.New()

	tests := []struct {
		name       string
		search     string
		expectBind bool
	}{
		{
			name:       "Valid search parameter",
			search:     "Option",
			expectBind: true,
		},
		{
			name:       "Empty search parameter",
			search:     "",
			expectBind: true,
		},
		{
			name:       "Search with special characters",
			search:     "Option-1",
			expectBind: true,
		},
		{
			name:       "No search parameter",
			search:     "",
			expectBind: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request URL with query parameter
			reqURL := "/option/list"
			if tt.search != "" {
				reqURL += "?search=" + url.QueryEscape(tt.search)
			}

			req := httptest.NewRequest(http.MethodGet, reqURL, nil)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			// Test just the binding part
			var reqStruct optionusecase.GetListReq
			err := c.Bind(&reqStruct)

			if tt.expectBind && err != nil {
				t.Errorf("Expected successful binding but got error: %v", err)
			}
			if !tt.expectBind && err == nil {
				t.Errorf("Expected binding error but got none")
			}

			// Verify the search parameter is correctly bound
			if tt.search != "" && reqStruct.Search != tt.search {
				t.Errorf("Expected search '%s', got '%s'", tt.search, reqStruct.Search)
			}
		})
	}
}

func TestGetListResponseStructure(t *testing.T) {
	// Test that the response structure matches the expected JSON format
	expectedResponse := map[string]interface{}{
		"message": "Success",
		"data": []map[string]interface{}{
			{
				"id":        float64(1),
				"name":      "Option 1",
				"add_claim": float64(7000),
			},
			{
				"id":        float64(2),
				"name":      "Option 2",
				"add_claim": float64(8000),
			},
		},
	}

	// Convert to JSON and back to verify structure
	jsonData, err := json.Marshal(expectedResponse)
	if err != nil {
		t.Fatalf("Failed to marshal expected response: %v", err)
	}

	var parsedResponse map[string]interface{}
	err = json.Unmarshal(jsonData, &parsedResponse)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	// Verify structure
	if parsedResponse["message"] != "Success" {
		t.Errorf("Expected message 'Success', got %v", parsedResponse["message"])
	}

	data, ok := parsedResponse["data"].([]interface{})
	if !ok {
		t.Fatalf("Expected data to be an array")
	}

	if len(data) != 2 {
		t.Errorf("Expected 2 items in data array, got %d", len(data))
	}

	// Check first item structure
	firstItem, ok := data[0].(map[string]interface{})
	if !ok {
		t.Fatalf("Expected first item to be an object")
	}

	if firstItem["id"] != float64(1) {
		t.Errorf("Expected id 1, got %v", firstItem["id"])
	}

	if firstItem["name"] != "Option 1" {
		t.Errorf("Expected name 'Option 1', got %v", firstItem["name"])
	}

	if firstItem["add_claim"] != float64(7000) {
		t.Errorf("Expected add_claim 7000, got %v", firstItem["add_claim"])
	}
}

// TestOptionListResponseStructure verifies the expected response format
func TestOptionListResponseStructure(t *testing.T) {
	// Test that the response types match the expected JSON structure
	resp := optionusecase.GetListResp{
		ID:       1,
		Name:     "Option 1",
		AddClaim: 7000,
	}

	// Verify the JSON tags are correct
	expectedJSON := `{"id":1,"name":"Option 1","add_claim":7000}`
	actualJSON, err := json.Marshal(resp)
	if err != nil {
		t.Errorf("Failed to marshal response: %v", err)
	}

	if string(actualJSON) != expectedJSON {
		t.Errorf("Expected JSON %s, got %s", expectedJSON, string(actualJSON))
	}

	// Test array response structure
	respArray := []optionusecase.GetListResp{
		{ID: 1, Name: "Option 1", AddClaim: 7000},
		{ID: 2, Name: "Option 2", AddClaim: 8000},
	}

	expectedArrayJSON := `[{"id":1,"name":"Option 1","add_claim":7000},{"id":2,"name":"Option 2","add_claim":8000}]`
	actualArrayJSON, err := json.Marshal(respArray)
	if err != nil {
		t.Errorf("Failed to marshal array response: %v", err)
	}

	if string(actualArrayJSON) != expectedArrayJSON {
		t.Errorf("Expected array JSON %s, got %s", expectedArrayJSON, string(actualArrayJSON))
	}
}
