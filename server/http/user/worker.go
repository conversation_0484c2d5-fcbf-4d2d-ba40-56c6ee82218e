package user

import (
	"net/http"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	"github.com/Sera-Global/be-nbs-accounting-system/server"
	userUsecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/user"
	"github.com/labstack/echo/v4"
)

func GetWorkerList(c echo.Context) error {
	var status int

	var req userUsecase.GetWorkerListReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	respData, err := server.UserUseCase.GetWorkerList(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.<PERSON>(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    respData,
	}
	status = http.StatusOK
	log.LogInfo("[user.GetWorkerList]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}
