package customer

import (
	customerusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/customer"
)

func validateGetDepartmentList(req customerusecase.GetDepartmentListReq) error {
	if req.CustomerID <= 0 {
		return ErrCustomerIDRequired
	}

	return nil
}

func validateGetDepartmentPicList(req customerusecase.GetDepartmentPicListReq) error {
	if req.DepartmentID <= 0 {
		return ErrDepartmentIDRequired
	}

	return nil
}
