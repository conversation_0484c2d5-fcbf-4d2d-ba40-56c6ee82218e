package basic_price

import (
	"net/http"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	"github.com/Sera-Global/be-nbs-accounting-system/server"
	basicPriceUsecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/basic_price"
	"github.com/labstack/echo/v4"
)

func GetList(c echo.Context) error {
	var status int

	var req basicPriceUsecase.GetListReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.<PERSON>SO<PERSON>(status, types.BasicResp{Message: err.Error()})
	}

	respData, err := server.BasicPriceUseCase.GetList(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.<PERSON>(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    respData,
	}
	status = http.StatusOK
	log.LogInfo("[basic_price.GetList]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}
