package distant_fee

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	distantfeeusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/distant_fee"
	"github.com/labstack/echo/v4"
)

func TestGetListRequestBinding(t *testing.T) {
	e := echo.New()

	tests := []struct {
		name       string
		search     string
		expectBind bool
	}{
		{
			name:       "Valid search parameter",
			search:     "Distant Fee",
			expectBind: true,
		},
		{
			name:       "Empty search parameter",
			search:     "",
			expectBind: true,
		},
		{
			name:       "Search with special characters",
			search:     "Distant-Fee-1",
			expectBind: true,
		},
		{
			name:       "No search parameter",
			search:     "",
			expectBind: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request with query parameters
			q := make(url.Values)
			if tt.search != "" {
				q.Set("search", tt.search)
			}

			req := httptest.NewRequest(http.MethodGet, "/?"+q.Encode(), nil)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			// Test request binding
			var reqData distantfeeusecase.GetListReq
			err := c.Bind(&reqData)

			if tt.expectBind && err != nil {
				t.Errorf("Expected successful binding, got error: %v", err)
			}

			if tt.expectBind && reqData.Search != tt.search {
				t.Errorf("Expected search '%s', got '%s'", tt.search, reqData.Search)
			}
		})
	}
}
