package block

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	blockusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/block"
	"github.com/labstack/echo/v4"
)

func TestGetListRequestBinding(t *testing.T) {
	e := echo.New()

	tests := []struct {
		name       string
		search     string
		expectBind bool
	}{
		{
			name:       "Valid search parameter",
			search:     "Bantul",
			expectBind: true,
		},
		{
			name:       "Empty search parameter",
			search:     "",
			expectBind: true,
		},
		{
			name:       "Search with special characters",
			search:     "Ban-tul",
			expectBind: true,
		},
		{
			name:       "No search parameter",
			search:     "",
			expectBind: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request URL with query parameter
			reqURL := "/block/list"
			if tt.search != "" {
				reqURL += "?search=" + url.QueryEscape(tt.search)
			}

			req := httptest.NewRequest(http.MethodGet, reqURL, nil)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			// Test just the binding part
			var reqStruct blockusecase.GetListReq
			err := c.Bind(&reqStruct)

			if tt.expectBind && err != nil {
				t.Errorf("Expected successful binding but got error: %v", err)
			}
			if !tt.expectBind && err == nil {
				t.Errorf("Expected binding error but got none")
			}

			// If binding was successful, check the search parameter
			if tt.expectBind && err == nil {
				if reqStruct.Search != tt.search {
					t.Errorf("Expected search '%s', got '%s'", tt.search, reqStruct.Search)
				}
			}
		})
	}
}

func TestGetListResponseStructure(t *testing.T) {
	// Test that the response structure matches the expected JSON format
	expectedResponse := map[string]interface{}{
		"message": "Success",
		"data": []map[string]interface{}{
			{
				"id":   float64(1),
				"name": "Bantul",
				"district": []interface{}{
					"Banguntapan",
					"Jetis",
				},
			},
			{
				"id":   float64(2),
				"name": "Sleman",
				"district": []interface{}{
					"Maguwoharjo",
					"Minomartani",
					"Ngaglik",
				},
			},
		},
	}

	// Convert to JSON and back to verify structure
	jsonData, err := json.Marshal(expectedResponse)
	if err != nil {
		t.Fatalf("Failed to marshal expected response: %v", err)
	}

	var parsedResponse map[string]interface{}
	err = json.Unmarshal(jsonData, &parsedResponse)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	// Verify structure
	if parsedResponse["message"] != "Success" {
		t.Errorf("Expected message 'Success', got %v", parsedResponse["message"])
	}

	data, ok := parsedResponse["data"].([]interface{})
	if !ok {
		t.Fatalf("Expected data to be an array")
	}

	if len(data) != 2 {
		t.Errorf("Expected 2 items in data array, got %d", len(data))
	}

	// Check first item structure
	firstItem, ok := data[0].(map[string]interface{})
	if !ok {
		t.Fatalf("Expected first item to be an object")
	}

	if firstItem["id"] != float64(1) {
		t.Errorf("Expected id 1, got %v", firstItem["id"])
	}

	if firstItem["name"] != "Bantul" {
		t.Errorf("Expected name 'Bantul', got %v", firstItem["name"])
	}

	districts, ok := firstItem["district"].([]interface{})
	if !ok {
		t.Fatalf("Expected district to be an array")
	}

	if len(districts) != 2 {
		t.Errorf("Expected 2 districts, got %d", len(districts))
	}

	if districts[0] != "Banguntapan" {
		t.Errorf("Expected first district 'Banguntapan', got %v", districts[0])
	}
}
