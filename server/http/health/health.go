package health

import (
	"context"
	"net/http"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	"github.com/Sera-Global/be-nbs-accounting-system/server"
	"github.com/labstack/echo/v4"
)

func Check(c echo.Context) error {
	var status int

	health, err := server.HealthUseCase.Health(context.Background())
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    health,
	}
	status = http.StatusOK
	log.LogInfo("[health.Check]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}
