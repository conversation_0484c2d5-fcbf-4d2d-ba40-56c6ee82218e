package site_report

import (
	"testing"
	"time"

	sitereport "github.com/Sera-Global/be-nbs-accounting-system/usecase/site_report"
)

func TestParseAndValidateCalculationVariableRequest_Success(t *testing.T) {
	req := &sitereport.GetStatutoryCalculationVariableReq{
		StartTime: "09:00:00",
		EndTime:   "17:00:00",
		BreakTime: "12:00:00",
	}

	err := parseAndValidateCalculationVariableRequest(req)

	if err != nil {
		t.<PERSON><PERSON><PERSON>("Expected no error, got %v", err)
	}

	expectedStartTime, _ := time.Parse("15:04:05", "09:00:00")
	if !req.ParsedStartTime.Equal(expectedStartTime) {
		t.<PERSON><PERSON><PERSON>("Expected ParsedStartTime %v, got %v", expectedStartTime, req.ParsedStartTime)
	}

	expectedEndTime, _ := time.Parse("15:04:05", "17:00:00")
	if !req.ParsedEndTime.Equal(expectedEndTime) {
		t.<PERSON><PERSON><PERSON>("Expected ParsedEndTime %v, got %v", expectedEndTime, req.ParsedEndTime)
	}

	if req.ParsedBreakTime == nil {
		t.Error("Expected ParsedBreakTime to be set, got nil")
	} else {
		expectedBreakTime, _ := time.Parse("15:04:05", "12:00:00")
		if !req.ParsedBreakTime.Equal(expectedBreakTime) {
			t.Errorf("Expected ParsedBreakTime %v, got %v", expectedBreakTime, *req.ParsedBreakTime)
		}
	}
}

func TestParseAndValidateCalculationVariableRequest_NoBreakTime(t *testing.T) {
	req := &sitereport.GetStatutoryCalculationVariableReq{
		StartTime: "09:00:00",
		EndTime:   "17:00:00",
		BreakTime: "",
	}

	err := parseAndValidateCalculationVariableRequest(req)

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if req.ParsedBreakTime != nil {
		t.Errorf("Expected ParsedBreakTime to be nil, got %v", *req.ParsedBreakTime)
	}
}

func TestParseAndValidateCalculationVariableRequest_InvalidStartTime(t *testing.T) {
	req := &sitereport.GetStatutoryCalculationVariableReq{
		StartTime: "invalid",
		EndTime:   "17:00:00",
	}

	err := parseAndValidateCalculationVariableRequest(req)

	if err == nil {
		t.Error("Expected error for invalid start time, got nil")
	}

	if err != ErrInvalidTimeFormat {
		t.Errorf("Expected ErrInvalidTimeFormat, got %v", err)
	}
}

func TestParseAndValidateCalculationVariableRequest_InvalidEndTime(t *testing.T) {
	req := &sitereport.GetStatutoryCalculationVariableReq{
		StartTime: "09:00:00",
		EndTime:   "invalid",
	}

	err := parseAndValidateCalculationVariableRequest(req)

	if err == nil {
		t.Error("Expected error for invalid end time, got nil")
	}

	if err != ErrInvalidTimeFormat {
		t.Errorf("Expected ErrInvalidTimeFormat, got %v", err)
	}
}

func TestParseAndValidateCalculationVariableRequest_InvalidBreakTime(t *testing.T) {
	req := &sitereport.GetStatutoryCalculationVariableReq{
		StartTime: "09:00:00",
		EndTime:   "17:00:00",
		BreakTime: "invalid",
	}

	err := parseAndValidateCalculationVariableRequest(req)

	if err == nil {
		t.Error("Expected error for invalid break time, got nil")
	}

	if err != ErrInvalidTimeFormat {
		t.Errorf("Expected ErrInvalidTimeFormat, got %v", err)
	}
}

func TestValidateGetStatutoryCalculationVariable_Success(t *testing.T) {
	startTime, _ := time.Parse("15:04:05", "09:00:00")
	endTime, _ := time.Parse("15:04:05", "17:00:00")

	req := sitereport.GetStatutoryCalculationVariableReq{
		CustomerID:      1,
		BasicPriceID:    1,
		StartTime:       "09:00:00",
		EndTime:         "17:00:00",
		ParsedStartTime: startTime,
		ParsedEndTime:   endTime,
	}

	err := validateGetStatutoryCalculationVariable(req)

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
}

func TestValidateGetStatutoryCalculationVariable_MissingCustomerID(t *testing.T) {
	req := sitereport.GetStatutoryCalculationVariableReq{
		CustomerID:   0,
		BasicPriceID: 1,
		StartTime:    "09:00:00",
		EndTime:      "17:00:00",
	}

	err := validateGetStatutoryCalculationVariable(req)

	if err == nil {
		t.Error("Expected error for missing customer ID, got nil")
	}

	if err != ErrCustomerIDRequired {
		t.Errorf("Expected ErrCustomerIDRequired, got %v", err)
	}
}

func TestValidateGetStatutoryCalculationVariable_MissingBasicPriceID(t *testing.T) {
	req := sitereport.GetStatutoryCalculationVariableReq{
		CustomerID:   1,
		BasicPriceID: 0,
		StartTime:    "09:00:00",
		EndTime:      "17:00:00",
	}

	err := validateGetStatutoryCalculationVariable(req)

	if err == nil {
		t.Error("Expected error for missing basic price ID, got nil")
	}

	if err != ErrBasicPriceIDRequired {
		t.Errorf("Expected ErrBasicPriceIDRequired, got %v", err)
	}
}

func TestValidateGetStatutoryCalculationVariable_MissingStartTime(t *testing.T) {
	req := sitereport.GetStatutoryCalculationVariableReq{
		CustomerID:   1,
		BasicPriceID: 1,
		StartTime:    "",
		EndTime:      "17:00:00",
	}

	err := validateGetStatutoryCalculationVariable(req)

	if err == nil {
		t.Error("Expected error for missing start time, got nil")
	}

	if err != ErrStartTimeRequired {
		t.Errorf("Expected ErrStartTimeRequired, got %v", err)
	}
}

func TestValidateGetStatutoryCalculationVariable_MissingEndTime(t *testing.T) {
	req := sitereport.GetStatutoryCalculationVariableReq{
		CustomerID:   1,
		BasicPriceID: 1,
		StartTime:    "09:00:00",
		EndTime:      "",
	}

	err := validateGetStatutoryCalculationVariable(req)

	if err == nil {
		t.Error("Expected error for missing end time, got nil")
	}

	if err != ErrEndTimeRequired {
		t.Errorf("Expected ErrEndTimeRequired, got %v", err)
	}
}

func TestValidateGetStatutoryCalculationVariable_StartTimeAfterEndTime(t *testing.T) {
	startTime, _ := time.Parse("15:04:05", "17:00:00")
	endTime, _ := time.Parse("15:04:05", "09:00:00")

	req := sitereport.GetStatutoryCalculationVariableReq{
		CustomerID:      1,
		BasicPriceID:    1,
		StartTime:       "17:00:00",
		EndTime:         "09:00:00",
		ParsedStartTime: startTime,
		ParsedEndTime:   endTime,
	}

	err := validateGetStatutoryCalculationVariable(req)

	if err == nil {
		t.Error("Expected error for start time after end time, got nil")
	}

	if err != ErrStartTimeAfterEndTime {
		t.Errorf("Expected ErrStartTimeAfterEndTime, got %v", err)
	}
}
