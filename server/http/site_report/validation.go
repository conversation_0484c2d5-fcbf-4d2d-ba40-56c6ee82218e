package site_report

import (
	"errors"
	"time"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	utils "github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/utils"
	sitereport "github.com/Sera-Global/be-nbs-accounting-system/usecase/site_report"
)

func validateGetList(req sitereport.GetListReq) error {
	if req.StartDate == "" {
		return ErrStartDateRequired
	}

	if req.EndDate == "" {
		return ErrEndDateRequired
	}

	// validate start date must be before end date
	if req.ParsedStartDate.After(req.ParsedEndDate) {
		return ErrStartDateAfterEndDate
	}

	return nil
}

func validateBulkUpdate(req sitereport.BulkUpdateReq) error {
	// Validate site_report_ids is required and not empty
	if len(req.SiteReportIDs) == 0 {
		return ErrSiteReportIDsRequired
	}

	// Validate at least one field is provided
	if req.WorkDate == nil && req.IsLocked == nil && req.IsInvoiceIssued == nil {
		return ErrNoFieldsProvided
	}

	// Validate date format if work_date is provided
	if req.WorkDate != nil {
		_, err := time.Parse("2006-01-02", *req.WorkDate)
		if err != nil {
			return ErrInvalidDateFormat
		}
	}

	// Role-based authorization checks
	if req.WorkDate != nil {
		// work_date updates: Only RoleAdmin and RoleSuperAdmin
		if !utils.HasRole(req.UserRoles, constanta.RoleAdmin, constanta.RoleSuperAdmin) {
			return errors.New(constanta.Unauthorized)
		}
	}

	if req.IsLocked != nil {
		if *req.IsLocked {
			// is_locked = true: Only RoleAdmin and RoleSuperAdmin
			if !utils.HasRole(req.UserRoles, constanta.RoleAdmin, constanta.RoleSuperAdmin) {
				return errors.New(constanta.Unauthorized)
			}
		} else {
			// is_locked = false: Only RoleSuperAdmin
			if !utils.HasRole(req.UserRoles, constanta.RoleSuperAdmin) {
				return errors.New(constanta.Unauthorized)
			}
		}
	}

	if req.IsInvoiceIssued != nil && *req.IsInvoiceIssued {
		// is_invoice_issued = true: RoleSubAdmin, RoleAdmin, and RoleSuperAdmin
		if !utils.HasRole(req.UserRoles, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin) {
			return errors.New(constanta.Unauthorized)
		}
	}

	return nil
}

// parseAndValidateCalculationVariableRequest parses and validates time parameters
func parseAndValidateCalculationVariableRequest(req *sitereport.GetStatutoryCalculationVariableReq) error {
	// Parse start_time
	startTime, err := time.Parse(time.TimeOnly, req.StartTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedStartTime = startTime

	// Parse end_time
	endTime, err := time.Parse(time.TimeOnly, req.EndTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedEndTime = endTime

	// Parse break_time if provided
	if req.BreakTime != "" {
		breakTime, err := time.Parse(time.TimeOnly, req.BreakTime)
		if err != nil {
			return ErrInvalidTimeFormat
		}
		req.ParsedBreakTime = &breakTime
	}

	return nil
}

// validateGetStatutoryCalculationVariable validates the calculation variable request
func validateGetStatutoryCalculationVariable(req sitereport.GetStatutoryCalculationVariableReq) error {
	if req.CustomerID <= 0 {
		return ErrCustomerIDRequired
	}

	if req.BasicPriceID <= 0 {
		return ErrBasicPriceIDRequired
	}

	if req.StartTime == "" {
		return ErrStartTimeRequired
	}

	if req.EndTime == "" {
		return ErrEndTimeRequired
	}

	// Validate start time is before end time
	if !req.ParsedStartTime.Before(req.ParsedEndTime) {
		return ErrStartTimeAfterEndTime
	}

	return nil
}

// parseAndValidateWorkerCalculationRequest parses and validates time parameters for worker calculation
func parseAndValidateWorkerCalculationRequest(req *sitereport.WorkerCalculationReq) error {
	// Parse start_time
	startTime, err := time.Parse(time.TimeOnly, req.StartTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedStartTime = startTime

	// Parse end_time
	endTime, err := time.Parse(time.TimeOnly, req.EndTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedEndTime = endTime

	// Parse break_time if provided
	if req.BreakTime != "" {
		breakTime, err := time.Parse(time.TimeOnly, req.BreakTime)
		if err != nil {
			return ErrInvalidTimeFormat
		}
		req.ParsedBreakTime = &breakTime
	}

	return nil
}

// validateWorkerCalculation validates the worker calculation request
func validateWorkerCalculation(req sitereport.WorkerCalculationReq) error {
	if req.BasicPriceID <= 0 {
		return ErrBasicPriceIDRequired
	}

	if req.StartTime == "" {
		return ErrStartTimeRequired
	}

	if req.EndTime == "" {
		return ErrEndTimeRequired
	}

	// Validate start time is before end time
	if !req.ParsedStartTime.Before(req.ParsedEndTime) {
		return ErrStartTimeAfterEndTime
	}

	// Validate transport expense is not negative
	if req.TransportExpense < 0 {
		return ErrNegativeTransportExpense
	}

	// Validate leader allowance is not negative
	if req.LeaderAllowance < 0 {
		return ErrNegativeLeaderAllowance
	}

	// Validate qualification allowance IDs if provided
	for _, id := range req.QualificationAllowanceIDs {
		if id <= 0 {
			return ErrInvalidQualificationID
		}
	}

	return nil
}

// parseAndValidateSaveWorkerRequest parses and validates time parameters for save worker request
func parseAndValidateSaveWorkerRequest(req *sitereport.SaveWorkerReq) error {
	// Parse start_time
	startTime, err := time.Parse(time.TimeOnly, req.StartTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedStartTime = startTime

	// Parse end_time
	endTime, err := time.Parse(time.TimeOnly, req.EndTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedEndTime = endTime

	// Parse break_time if provided
	if req.BreakTime != "" {
		breakTime, err := time.Parse(time.TimeOnly, req.BreakTime)
		if err != nil {
			return ErrInvalidTimeFormat
		}
		req.ParsedBreakTime = &breakTime
	}

	return nil
}

// validateSaveWorker validates the save worker request
func validateSaveWorker(req sitereport.SaveWorkerReq) error {
	if req.ID == 0 {
		return ErrIDRequired
	}

	if req.UserID <= 0 {
		return ErrUserIDRequired
	}

	if req.BasicPriceID <= 0 {
		return ErrBasicPriceIDRequired
	}

	if req.StartTime == "" {
		return ErrStartTimeRequired
	}

	if req.EndTime == "" {
		return ErrEndTimeRequired
	}

	// Validate start time is before end time
	if !req.ParsedStartTime.Before(req.ParsedEndTime) {
		return ErrStartTimeAfterEndTime
	}

	// Validate transport expense is not negative
	if req.TransportExpense < 0 {
		return ErrNegativeTransportExpense
	}

	// Validate leader allowance is not negative
	if req.LeaderAllowance < 0 {
		return ErrNegativeLeaderAllowance
	}

	// Validate qualification allowance IDs if provided
	for _, id := range req.QualificationAllowanceIDs {
		if id <= 0 {
			return ErrInvalidQualificationID
		}
	}

	return nil
}
