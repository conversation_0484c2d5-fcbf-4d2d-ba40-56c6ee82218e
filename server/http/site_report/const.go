package site_report

import "errors"

var (
	ErrStartDateRequired     = errors.New("開始日は必須です")
	ErrEndDateRequired       = errors.New("終了日は必須です")
	ErrInvalidDateFormat     = errors.New("日付形式が無効です。YYYY-MM-DD形式で入力してください")
	ErrSiteReportIDsRequired = errors.New("サイトレポートIDは必須です")
	ErrNoFieldsProvided      = errors.New("更新するフィールドが指定されていません")
	ErrStartDateAfterEndDate = errors.New("開始日は終了日以降の日付を入力してください")

	// Calculation variable errors
	ErrCustomerIDRequired    = errors.New("customer_id is required")
	ErrBasicPriceIDRequired  = errors.New("basic_price_id is required")
	ErrStartTimeRequired     = errors.New("start_time is required")
	ErrEndTimeRequired       = errors.New("end_time is required")
	ErrInvalidTimeFormat     = errors.New("invalid time format. Use HH:MM:SS format")
	ErrStartTimeAfterEndTime = errors.New("start_time must be before end_time")

	// Worker calculation errors
	ErrNegativeTransportExpense = errors.New("transport_expense cannot be negative")
	ErrNegativeLeaderAllowance  = errors.New("leader_allowance cannot be negative")
	ErrInvalidQualificationID   = errors.New("qualification_allowance_ids must contain valid IDs greater than 0")

	// Save worker errors
	ErrIDRequired     = errors.New("ID is required")
	ErrUserIDRequired = errors.New("user_id is required and must be positive")
)
