package customer

import (
    "time"

    "github.com/GoAdminGroup/go-admin/context"
    "github.com/GoAdminGroup/go-admin/modules/db"
    "github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
    "github.com/GoAdminGroup/go-admin/template/types/form"

    "github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// GetTransferDestinationTable configures GoAdmin CRUD for the transfer_destination table.
func GetTransferDestinationTable(ctx *context.Context) table.Table {
    tbl := table.NewDefaultTable(ctx, table.Config{
        Driver:     db.DriverPostgresql,
        CanAdd:     true,
        Editable:   true,
        Deletable:  true,
        Exportable: true,
        Connection: table.DefaultConnectionName,
        PrimaryKey: table.PrimaryKey{
            Type: db.Bigint,
            Name: table.DefaultPrimaryKeyName,
        },
    })

    info := tbl.GetInfo()
    gormDB := dbmanager.Manager()

    // Fields
    info.AddField("ID", "id", db.Bigint).FieldSortable()
    info.AddField("振込先コード", "code", db.Varchar)
    info.AddField("銀行名", "bank_name", db.Varchar)
    info.AddField("支店名", "branch", db.Varchar)
    info.AddField("口座番号", "account_number", db.Varchar)
    info.AddField("作成日", "created_at", db.Timestamp)
    info.AddField("更新日", "updated_at", db.Timestamp)

    info.SetTable("transfer_destination").SetTitle("振込先一覧")

    // Soft delete
    info.SetDeleteFn(func(ids []string) error {
        if gormDB == nil {
            return nil
        }
        return gormDB.Table("transfer_destination").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
    })

    // Exclude soft-deleted rows
    info.WhereRaw("\"transfer_destination\".deleted_at IS NULL")

    // Form configuration
    formList := tbl.GetForm()

    formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
    formList.AddField("振込先コード", "code", db.Varchar, form.Text).FieldMust()
    formList.AddField("銀行名", "bank_name", db.Varchar, form.Text).FieldMust()
    formList.AddField("支店名", "branch", db.Varchar, form.Text).FieldMust()
    formList.AddField("口座番号", "account_number", db.Varchar, form.Text).FieldMust()

    formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
        FieldNowWhenInsert().
        FieldHideWhenCreate().
        FieldHideWhenUpdate().
        FieldNotAllowEdit()

    formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
        FieldNow().
        FieldHideWhenCreate().
        FieldHideWhenUpdate().
        FieldNotAllowEdit()

    formList.SetTable("transfer_destination").SetTitle("振込先")

    return tbl
}
