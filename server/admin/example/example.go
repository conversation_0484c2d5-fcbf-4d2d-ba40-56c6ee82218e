// REMOVE THIS FILE IF NOT NEEDED
package example

import (
	"encoding/json"
	"fmt"
	"html/template"
	"strconv"
	"strings"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	form2 "github.com/GoAdminGroup/go-admin/plugins/admin/modules/form"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"
)

func GetExampleTable(ctx *context.Context) table.Table {
	// Assuming you have an example table in your database, example:
	// CREATE TABLE example (
	// 	id SERIAL PRIMARY KEY,
	// 	name VARCHAR(50) DEFAULT NULL,
	// 	gender SMALLINT DEFAULT NULL,
	// 	city VARCHAR(50) DEFAULT NULL,
	// 	ip VARCHAR(20) DEFAULT NULL,
	// 	phone VARCHAR(10) DEFAULT NULL,
	// 	pricing_json JSON DEFAULT '[]'::json,
	// 	created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
	// 	updated_at TIMESTAMPTZ DEFAULT NULL
	// );

	// config the table model.
	exampleTable := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Int,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := exampleTable.GetInfo()

	// set id sortable.
	info.AddField("ID", "id", db.Int).FieldSortable()
	info.AddField("Name", "name", db.Varchar)

	// use FieldDisplay.
	info.AddField("Gender", "gender", db.Tinyint).FieldDisplay(func(model types.FieldModel) interface{} {
		if model.Value == "0" {
			return "men"
		}
		if model.Value == "1" {
			return "women"
		}
		return "unknown"
	})

	info.AddField("Phone", "phone", db.Varchar)
	info.AddField("City", "city", db.Varchar)
	info.AddField("Pricing", "pricing_json", db.JSON).FieldDisplay(func(model types.FieldModel) interface{} {
		var pricingData []map[string]interface{}
		if model.Value == "" {
			return "No pricing data"
		}
		if err := json.Unmarshal([]byte(model.Value), &pricingData); err != nil {
			return "Error parsing pricing data"
		}

		var tableContent string
		tableContent += `<div class="box">
			<div class="box-header with-border">
				<h3 class="box-title"><i class="fa fa-money"></i> Pricing Information</h3>
			</div>
			<div class="box-body">
				<table style="width:100%;border-collapse:collapse;margin:10px 0;">
					<thead>
						<tr>
							<th style="border:1px solid #ddd;padding:8px;text-align:left;">Hour</th>
							<th style="border:1px solid #ddd;padding:8px;text-align:left;">Price</th>
						</tr>
					</thead>
					<tbody>`

		for _, item := range pricingData {
			hour, _ := item["hour"].(string)
			price, _ := item["price"].(float64)
			tableContent += fmt.Sprintf(`
						<tr>
							<td style="border:1px solid #ddd;padding:8px;">%s</td>
							<td style="border:1px solid #ddd;padding:8px;">%.0f</td>
						</tr>`, hour, price)
		}

		tableContent += `
					</tbody>
				</table>
			</div>
		</div>`

		return template.HTML(tableContent)
	})
	info.AddField("CreatedAt", "created_at", db.Timestamp)
	info.AddField("UpdatedAt", "updated_at", db.Timestamp)

	// set the title and description of table page.
	info.SetTable("example").SetTitle("Example").SetDescription("Example").
		SetAction(template.HTML(`<a href="http://google.com"><i class="fa fa-google"></i></a>`)) // custom operation button

	formList := exampleTable.GetForm()

	// set id editable is false.
	formList.AddField("ID", "id", db.Int, form.Default).FieldNotAllowEdit()
	formList.AddField("Ip", "ip", db.Varchar, form.Text)
	formList.AddField("Name", "name", db.Varchar, form.Text)

	// use FieldOptions.
	formList.AddField("Gender", "gender", db.Tinyint, form.Radio).
		FieldOptions(types.FieldOptions{
			{
				Text:  "male",
				Value: "0",
			}, {
				Text:  "female",
				Value: "1",
			},
		}).FieldDefault("0")
	formList.AddField("Phone", "phone", db.Varchar, form.Text)
	formList.AddField("City", "city", db.Varchar, form.Text)
	// Add the pricing_json field as a hidden field
	formList.AddField("Pricing", "pricing_json", db.JSON, form.Text).
		FieldHide().
		FieldDefault("[]")

	// Add hourly price input fields from 00:00 to 23:00.
	for i := 0; i < 24; i++ {
		hour := fmt.Sprintf("%02d:00", i)
		col := fmt.Sprintf("price_%02d", i)
		formList.AddField(hour, col, db.Decimal, form.Text).
			FieldOptionExt(map[string]interface{}{
				"style": "width: 100px",
				"class": "price-input",
			})
	}

	// add a custom field and use FieldPostFilterFn to do more things.
	formList.AddField("Custom Field", "role", db.Varchar, form.Text).
		FieldPostFilterFn(func(value types.PostFieldModel) interface{} {
			fmt.Println("user custom field", value)
			return ""
		})

	formList.AddField("UpdatedAt", "updated_at", db.Timestamp, form.Default).FieldNotAllowAdd()
	formList.AddField("CreatedAt", "created_at", db.Timestamp, form.Default).FieldNotAllowAdd()

	// use SetTabGroups to group a form into tabs
	// First, collect all price field names
	var priceFields []string
	for i := 0; i < 24; i++ {
		priceFields = append(priceFields, fmt.Sprintf("price_%02d", i))
	}

	formList.SetTabGroups(types.
		NewTabGroups("id", "ip", "name", "gender", "city").
		AddGroup("phone", "role").
		AddGroup(priceFields...)).
		SetTabHeaders("Basic Info", "Contact", "Pricing")

	// set the title and description of form page.
	formList.SetTable("example").SetTitle("Example").SetDescription("Example")

	// use SetPreProcessFn to add operation when form posted.
	// Build pricing_json from hourly price inputs before saving.
	formList.SetPreProcessFn(func(values form2.Values) form2.Values {
		var pricing []map[string]interface{}
		for i := 0; i < 24; i++ {
			hour := fmt.Sprintf("%02d:00", i)
			key := fmt.Sprintf("price_%02d", i)
			priceStr := values.Get(key)
			// Remove the custom field so it won't attempt to insert into DB.
			delete(values, key)

			if priceStr == "" {
				priceStr = "0"
			}
			// Handle both comma and dot as decimal separator
			priceStr = strings.Replace(priceStr, ",", ".", 1)
			price, _ := strconv.ParseFloat(priceStr, 64)
			pricing = append(pricing, map[string]interface{}{"hour": hour, "price": price})
		}

		// Convert pricing data to JSON string
		b, err := json.Marshal(pricing)
		if err != nil {
			fmt.Printf("Error marshaling pricing data: %v\n", err)
			return values
		}

		// Set the pricing_json field
		values.Add("pricing_json", string(b))
		return values
	})

	return exampleTable
}
