// This file is generated by GoAdmin CLI adm.
package admin

import (
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/Sera-Global/be-nbs-accounting-system/server/admin/customer"
	"github.com/Sera-Global/be-nbs-accounting-system/server/admin/example"
	"github.com/Sera-Global/be-nbs-accounting-system/server/admin/report"
	"github.com/Sera-Global/be-nbs-accounting-system/server/admin/user"
)

// The key of Generators is the prefix of table info url.
// The corresponding value is the Form and Table data.
//
// http://{{config.Domain}}:{{Port}}/{{config.Prefix}}/info/{{key}}
//
// example:
//
// "example" => http://localhost:9033/admin/info/example
//
// example end
var Generators = map[string]table.Generator{

	"example": example.GetExampleTable,

	"user": user.GetUserTable,
	"role": user.GetRoleTable,

	"transfer-destination": customer.GetTransferDestinationTable,
	"customer":             customer.GetCustomerTable,
	"department":           customer.GetDepartmentTable,
	"department-pic":       customer.GetDepartmentPICTable,
	"statutory":            customer.GetStatutoryTable,

	"distant-fee":              report.GetDistantFeeTable,
	"daily-report-addition":    report.GetDailyReportAdditionTable,
	"qualification":            report.GetQualificationTable,
	"distant-travel-allowance": report.GetDistantTravelAllowanceTable,
	"option":                   report.GetOptionTable,
	"income-tax":               report.GetIncomeTaxTable,
	"consumption-tax":          report.GetConsumptionTaxTable,
	"district":                 report.GetDistrictTable,
	"block":                    report.GetBlockTable,
	"basic-price":              report.GetBasicPriceTable,

	// generators end
}
