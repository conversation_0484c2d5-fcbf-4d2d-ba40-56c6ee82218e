package user

import (
	"time"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"

	"github.com/GoAdminGroup/go-admin/template/types/form"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// GetRoleTable returns the GoAdmin configuration for the role table.
func GetRoleTable(ctx *context.Context) table.Table {
	// configure table model
	roleTable := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Int,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := roleTable.GetInfo()
	gormDB := dbmanager.Manager()

	// List fields
	info.AddField("ID", "id", db.Int).FieldSortable()
	info.AddField("役割名", "name", db.Varchar)
	info.AddField("作成日", "created_at", db.Timestamp)
	info.AddField("更新日", "updated_at", db.Timestamp)

	info.SetTable("role").SetTitle("役割")

	// Soft delete implementation
	info.SetDeleteFn(func(ids []string) error {
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("role").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude soft-deleted records
	info.WhereRaw("\"role\".deleted_at IS NULL")

	formList := roleTable.GetForm()

	formList.AddField("ID", "id", db.Int, form.Default).FieldNotAllowEdit()
	formList.AddField("役割名", "name", db.Varchar, form.Text).FieldMust()

	// created_at timestamp
	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit()

	// updated_at timestamp
	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit()

	formList.SetTable("role").SetTitle("役割")

	return roleTable
}
