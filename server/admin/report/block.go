package report

import (
    "time"

    "github.com/GoAdminGroup/go-admin/context"
    "github.com/GoAdminGroup/go-admin/modules/db"
    "github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
    "github.com/GoAdminGroup/go-admin/template/types/form"

    "github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// GetBlockTable configures GoAdmin CRUD for the block table.
func GetBlockTable(ctx *context.Context) table.Table {
    tbl := table.NewDefaultTable(ctx, table.Config{
        Driver:     db.DriverPostgresql,
        CanAdd:     true,
        Editable:   true,
        Deletable:  true,
        Exportable: true,
        Connection: table.DefaultConnectionName,
        PrimaryKey: table.PrimaryKey{
            Type: db.Bigint,
            Name: table.DefaultPrimaryKeyName,
        },
    })

    info := tbl.GetInfo()
    gormDB := dbmanager.Manager()

    // List fields
    info.AddField("ID", "id", db.Bigint).FieldSortable()
    info.AddField("ブロックコード", "code", db.Varchar)
    info.AddField("名前", "name", db.Varchar)
    info.AddField("単価", "unit_price", db.Float)
    info.AddField("人件費", "price_per_worker", db.Float)
    info.AddField("作成日", "created_at", db.Timestamp)
    info.AddField("更新日", "updated_at", db.Timestamp)

    info.SetTable("block").SetTitle("ブロック")

    // Soft delete implementation
    info.SetDeleteFn(func(ids []string) error {
        if gormDB == nil {
            return nil
        }
        return gormDB.Table("block").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
    })

    // Exclude soft-deleted records
    info.WhereRaw("\"block\".deleted_at IS NULL")

    // Form configuration
    formList := tbl.GetForm()

    formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
    formList.AddField("ブロックコード", "code", db.Varchar, form.Text).FieldMust()
    formList.AddField("名前", "name", db.Varchar, form.Text).FieldMust()
    formList.AddField("単価", "unit_price", db.Float, form.Number).FieldMust()
    formList.AddField("人件費", "price_per_worker", db.Float, form.Number).FieldMust()

    formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
        FieldNowWhenInsert().
        FieldHideWhenCreate().
        FieldHideWhenUpdate().
        FieldNotAllowEdit()

    formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
        FieldNow().
        FieldHideWhenCreate().
        FieldHideWhenUpdate().
        FieldNotAllowEdit()

    formList.SetTable("block").SetTitle("ブロック")

    return tbl
}
