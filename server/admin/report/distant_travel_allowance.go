package report

import (
	"time"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types/form"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// GetDistantTravelAllowanceTable configures GoAdmin CRUD for the distant_travel_allowance table.
func GetDistantTravelAllowanceTable(ctx *context.Context) table.Table {
	tbl := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := tbl.GetInfo()
	gormDB := dbmanager.Manager()

	// List fields
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("加算額コード", "code", db.Varchar)
	info.AddField("表示タイトル", "title", db.Varchar)
	info.AddField("説明", "explanation", db.Text)
	info.AddField("加算金(請求)", "add_claim", db.Float)
	info.AddField("加算額(支給)", "add_paid", db.Float)
	info.AddField("作成日", "created_at", db.Timestamp)
	info.AddField("更新日", "updated_at", db.Timestamp)

	info.SetTable("distant_travel_allowance").SetTitle("遠方出張手当")

	// Soft delete
	info.SetDeleteFn(func(ids []string) error {
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("distant_travel_allowance").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude soft-deleted records
	info.WhereRaw("\"distant_travel_allowance\".deleted_at IS NULL")

	// Form configuration
	formList := tbl.GetForm()

	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("加算額コード", "code", db.Varchar, form.Text).FieldMust()
	formList.AddField("表示タイトル", "title", db.Varchar, form.Text).FieldMust()
	formList.AddField("説明", "explanation", db.Text, form.RichText).FieldMust()
	formList.AddField("加算金(請求)", "add_claim", db.Float, form.Number).FieldMust()
	formList.AddField("加算額(支給)", "add_paid", db.Float, form.Number).FieldMust()

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit()

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit()

	formList.SetTable("distant_travel_allowance").SetTitle("遠方出張手当")

	return tbl
}
