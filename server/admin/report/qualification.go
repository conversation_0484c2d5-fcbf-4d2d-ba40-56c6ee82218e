package report

import (
	"time"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types/form"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// GetQualificationTable configures GoAdmin CRUD for the qualification table.
func GetQualificationTable(ctx *context.Context) table.Table {
	tbl := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := tbl.GetInfo()
	gormDB := dbmanager.Manager()

	// List fields
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("加算額コード", "code", db.Varchar)
	info.AddField("表示タイトル", "title", db.Varchar)
	info.AddField("説明", "explanation", db.Text)
	info.AddField("加算金(請求)", "add_claim", db.Float)
	info.AddField("加算額(支給)", "paid_amount", db.Float)
	info.AddField("作成日", "created_at", db.Timestamp)
	info.AddField("更新日", "updated_at", db.Timestamp)

	info.SetTable("qualification").SetTitle("資格一覧")

	// Soft delete logic
	info.SetDeleteFn(func(ids []string) error {
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("qualification").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude deleted records
	info.WhereRaw("\"qualification\".deleted_at IS NULL")

	// Form config
	formList := tbl.GetForm()

	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("加算額コード", "code", db.Varchar, form.Text).FieldMust()
	formList.AddField("表示タイトル", "title", db.Varchar, form.Text).FieldMust()
	formList.AddField("説明", "explanation", db.Text, form.RichText).FieldMust()
	formList.AddField("加算金(請求)", "add_claim", db.Float, form.Number).FieldMust()
	formList.AddField("加算額(支給", "paid_amount", db.Float, form.Number).FieldMust()

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit()

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit()

	formList.SetTable("qualification").SetTitle("資格")

	return tbl
}
