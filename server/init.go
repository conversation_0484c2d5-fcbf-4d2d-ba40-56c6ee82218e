package server

import (
	basicPriceDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
	blockDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/block"
	customerDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/customer"
	dailyreportadditionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/daily_report_addition"
	departmentDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/department"
	departmentpicDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/department_pic"
	distantfeeDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/distant_fee"
	healthDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/health"
	incometaxDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/income_tax"
	optionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/option"
	qualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/qualification"
	sitereportDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report"
	statutoryDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/statutory"
	userDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user"
	userRoleDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user_role"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/config"
	basicpriceusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/basic_price"
	blockusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/block"
	customerusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/customer"
	distantfeeusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/distant_fee"
	healthusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/health"
	optionusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/option"
	qualificationusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/qualification"
	sitereportusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/site_report"
	userusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/user"
)

var (
	// usecases
	HealthUseCase        *healthusecase.HealthUseCase
	UserUseCase          *userusecase.UserUseCase
	SiteReportUseCase    *sitereportusecase.SiteReportUseCase
	CustomerUseCase      *customerusecase.CustomerUseCase
	BasicPriceUseCase    *basicpriceusecase.BasicPriceUseCase
	BlockUseCase         *blockusecase.BlockUseCase
	OptionUseCase        *optionusecase.OptionUseCase
	QualificationUseCase *qualificationusecase.QualificationUseCase
	DistantFeeUseCase    *distantfeeusecase.DistantFeeUseCase

	// domains
	healthDomain              healthDmn.HealthDomain
	userDomain                userDmn.UserDomain
	userRoleDomain            userRoleDmn.UserRoleDomain
	siteReportDomain          sitereportDmn.SiteReportDomain
	customerDomain            customerDmn.CustomerDomain
	departmentDomain          departmentDmn.DepartmentDomain
	departmentPicDomain       departmentpicDmn.DepartmentPicDomain
	distantFeeDomain          distantfeeDmn.DistantFeeDomain
	incomeTaxDomain           incometaxDmn.IncomeTaxDomain
	basicPriceDomain          basicPriceDmn.BasicPriceDomain
	blockDomain               blockDmn.BlockDomain
	optionDomain              optionDmn.OptionDomain
	qualificationDomain       qualificationDmn.QualificationDomain
	statutoryDomain           statutoryDmn.StatutoryDomain
	dailyReportAdditionDomain dailyreportadditionDmn.DailyReportAdditionDomain
)

func Init(mode ...string) error {
	config.InitDatabase()
	config.InitGoAdmin()
	InitAccountingSystem()

	return nil
}

func InitAccountingSystem() {
	healthDomain = healthDmn.InitHealthDomain(healthDmn.HealthResource{})
	userDomain = userDmn.InitUserDomain(userDmn.UserResource{})
	userRoleDomain = userRoleDmn.InitUserRoleDomain(userRoleDmn.UserRoleResource{})
	siteReportDomain = sitereportDmn.InitSiteReportDomain(sitereportDmn.SiteReportResource{})
	customerDomain = customerDmn.InitCustomerDomain(customerDmn.CustomerResource{})
	departmentDomain = departmentDmn.InitDepartmentDomain(departmentDmn.DepartmentResource{})
	departmentPicDomain = departmentpicDmn.InitDepartmentPicDomain(departmentpicDmn.DepartmentPicResource{})
	basicPriceDomain = basicPriceDmn.InitBasicPriceDomain(basicPriceDmn.BasicPriceResource{})
	blockDomain = blockDmn.InitBlockDomain(blockDmn.BlockResource{})
	optionDomain = optionDmn.InitOptionDomain(optionDmn.OptionResource{})
	qualificationDomain = qualificationDmn.InitQualificationDomain(qualificationDmn.QualificationResource{})
	distantFeeDomain = distantfeeDmn.InitDistantFeeDomain(distantfeeDmn.DistantFeeResource{})
	incomeTaxDomain = incometaxDmn.InitIncomeTaxDomain(incometaxDmn.IncomeTaxResource{})
	statutoryDomain = statutoryDmn.InitStatutoryDomain(statutoryDmn.StatutoryResource{})
	dailyReportAdditionDomain = dailyreportadditionDmn.InitDailyReportAdditionDomain(dailyreportadditionDmn.DailyReportAdditionResource{})

	healthDomains := healthusecase.Domains{
		HealthDomain: &healthDomain,
	}

	userDomains := userusecase.Domains{
		UserDomain:     &userDomain,
		UserRoleDomain: &userRoleDomain,
	}

	siteReportDomains := sitereportusecase.Domains{
		SiteReportDomain:          &siteReportDomain,
		BasicPriceDomain:          &basicPriceDomain,
		StatutoryDomain:           &statutoryDomain,
		DailyReportAdditionDomain: &dailyReportAdditionDomain,
		DistantFeeDomain:          &distantFeeDomain,
		IncomeTaxDomain:           &incomeTaxDomain,
		QualificationDomain:       &qualificationDomain,
	}

	customerDomains := customerusecase.Domains{
		CustomerDomain:      &customerDomain,
		DepartmentDomain:    &departmentDomain,
		DepartmentPicDomain: &departmentPicDomain,
	}

	basicPriceDomains := basicpriceusecase.Domains{
		BasicPriceDomain: &basicPriceDomain,
	}

	blockDomains := blockusecase.Domains{
		BlockDomain: &blockDomain,
	}

	optionDomains := optionusecase.Domains{
		OptionDomain: &optionDomain,
	}

	qualificationDomains := qualificationusecase.Domains{
		QualificationDomain: &qualificationDomain,
	}

	distantFeeDomains := distantfeeusecase.Domains{
		DistantFeeDomain: &distantFeeDomain,
	}

	HealthUseCase = healthusecase.InitHealthUseCase(healthDomains)
	UserUseCase = userusecase.InitUserUseCase(userDomains)
	SiteReportUseCase = sitereportusecase.InitSiteReportUseCase(siteReportDomains)
	CustomerUseCase = customerusecase.InitCustomerUseCase(customerDomains)
	BasicPriceUseCase = basicpriceusecase.InitBasicPriceUseCase(basicPriceDomains)
	BlockUseCase = blockusecase.InitBlockUseCase(blockDomains)
	OptionUseCase = optionusecase.InitOptionUseCase(optionDomains)
	QualificationUseCase = qualificationusecase.InitQualificationUseCase(qualificationDomains)
	DistantFeeUseCase = distantfeeusecase.InitDistantFeeUseCase(distantFeeDomains)
}
